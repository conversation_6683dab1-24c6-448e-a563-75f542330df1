// Package cybertheme provides the cyberpunk aesthetic theme for Assistant-Go
// This package implements the beloved cyberpunk/hacker aesthetic that users love
package cybertheme

import (
	"fmt"
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/theme"
)

// CyberTheme implements the cyberpunk aesthetic theme
type CyberTheme struct {
	// Core colors
	primaryColor   color.Color
	secondaryColor color.Color
	backgroundColor color.Color
	surfaceColor   color.Color

	// Text colors
	textColor       color.Color
	textSecondary   color.Color
	textDisabled    color.Color

	// Status colors
	successColor color.Color
	warningColor color.Color
	errorColor   color.Color

	// Special cyberpunk colors
	neonGreen   color.Color
	neonBlue    color.Color
	neonPink    color.Color
	terminalGreen color.Color

	// Font settings
	fontFamily string
	fontSize   float32
}

// NewCyberTheme creates a new cyberpunk theme with default colors
func NewCyberTheme() *CyberTheme {
	return &CyberTheme{
		// Core cyberpunk colors - dark background with neon accents
		primaryColor:   color.NRGBA{R: 0, G: 255, B: 0, A: 255},     // Bright green
		secondaryColor: color.NRGBA{R: 255, G: 0, B: 128, A: 255},   // Neon pink
		backgroundColor: color.NRGBA{R: 10, G: 10, B: 15, A: 255},   // Very dark blue-black
		surfaceColor:   color.NRGBA{R: 20, G: 25, B: 30, A: 255},    // Dark surface

		// Text colors for good readability
		textColor:     color.NRGBA{R: 0, G: 255, B: 0, A: 255},      // Bright green
		textSecondary: color.NRGBA{R: 0, G: 200, B: 200, A: 255},    // Cyan
		textDisabled:  color.NRGBA{R: 100, G: 100, B: 100, A: 255},  // Gray

		// Status colors
		successColor: color.NRGBA{R: 0, G: 255, B: 100, A: 255},     // Bright green
		warningColor: color.NRGBA{R: 255, G: 170, B: 0, A: 255},     // Amber
		errorColor:   color.NRGBA{R: 255, G: 0, B: 64, A: 255},      // Bright red

		// Special neon colors
		neonGreen:     color.NRGBA{R: 0, G: 255, B: 0, A: 255},
		neonBlue:      color.NRGBA{R: 0, G: 255, B: 255, A: 255},
		neonPink:      color.NRGBA{R: 255, G: 0, B: 128, A: 255},
		terminalGreen: color.NRGBA{R: 0, G: 255, B: 0, A: 255},

		// Font settings
		fontFamily: "JetBrains Mono",
		fontSize:   12.0,
	}
}

// NewCustomCyberTheme creates a cyberpunk theme with custom colors
func NewCustomCyberTheme(primary, secondary, background string) *CyberTheme {
	theme := NewCyberTheme()

	if primary != "" {
		if c, err := parseHexColor(primary); err == nil {
			theme.primaryColor = c
			theme.textColor = c
			theme.neonGreen = c
		}
	}

	if secondary != "" {
		if c, err := parseHexColor(secondary); err == nil {
			theme.secondaryColor = c
			theme.neonPink = c
		}
	}

	if background != "" {
		if c, err := parseHexColor(background); err == nil {
			theme.backgroundColor = c
		}
	}

	return theme
}

// Color returns colors for various UI elements
func (t *CyberTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	switch name {
	// Primary colors
	case theme.ColorNamePrimary:
		return t.primaryColor
	case theme.ColorNameBackground:
		return t.backgroundColor

	// Text colors
	case theme.ColorNameForeground:
		return t.textColor

	// Status colors
	case theme.ColorNameSuccess:
		return t.successColor
	case theme.ColorNameWarning:
		return t.warningColor
	case theme.ColorNameError:
		return t.errorColor

	// Input and interactive elements
	case theme.ColorNameInputBackground:
		return t.surfaceColor
	case theme.ColorNameInputBorder:
		return t.primaryColor
	case theme.ColorNameButton:
		return t.primaryColor
	case theme.ColorNameDisabled:
		return t.textDisabled
	case theme.ColorNamePlaceHolder:
		return t.textSecondary

	// Selection and focus
	case theme.ColorNameSelection:
		return color.NRGBA{R: 0, G: 255, B: 0, A: 80} // Semi-transparent green
	case theme.ColorNameFocus:
		return t.primaryColor
	case theme.ColorNamePressed:
		return color.NRGBA{R: 0, G: 200, B: 0, A: 255} // Darker green
	case theme.ColorNameHover:
		return color.NRGBA{R: 0, G: 255, B: 0, A: 40}  // Very light green

	// Scrollbar and separators
	case theme.ColorNameScrollBar:
		return t.textSecondary
	case theme.ColorNameSeparator:
		return t.textSecondary

	// Menu and overlay
	case theme.ColorNameMenuBackground:
		return t.surfaceColor
	case theme.ColorNameOverlayBackground:
		return color.NRGBA{R: 0, G: 0, B: 0, A: 180}

	default:
		// Fallback to primary color for unknown colors
		return t.primaryColor
	}
}

// Font returns the font for various text elements
func (t *CyberTheme) Font(style fyne.TextStyle) fyne.Resource {
	// TODO: Load custom cyberpunk fonts
	// For now, return default font - will be enhanced later
	return theme.DefaultTheme().Font(style)
}

// Icon returns icons with cyberpunk styling
func (t *CyberTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	// TODO: Create custom cyberpunk icons
	// For now, return default icons - will be enhanced later
	return theme.DefaultTheme().Icon(name)
}

// Size returns sizes for various UI elements
func (t *CyberTheme) Size(name fyne.ThemeSizeName) float32 {
	switch name {
	case theme.SizeNameText:
		return t.fontSize
	case theme.SizeNameCaptionText:
		return t.fontSize - 2
	case theme.SizeNameHeadingText:
		return t.fontSize + 4
	case theme.SizeNameSubHeadingText:
		return t.fontSize + 2
	case theme.SizeNameInlineIcon:
		return t.fontSize
	case theme.SizeNamePadding:
		return 8
	case theme.SizeNameScrollBar:
		return 12
	case theme.SizeNameScrollBarSmall:
		return 8
	case theme.SizeNameSeparatorThickness:
		return 1
	case theme.SizeNameInputBorder:
		return 2
	case theme.SizeNameInputRadius:
		return 4
	case theme.SizeNameSelectionRadius:
		return 4
	default:
		return theme.DefaultTheme().Size(name)
	}
}

// GetNeonColor returns special neon colors for custom widgets
func (t *CyberTheme) GetNeonColor(name string) color.Color {
	switch name {
	case "green":
		return t.neonGreen
	case "blue":
		return t.neonBlue
	case "pink":
		return t.neonPink
	case "terminal":
		return t.terminalGreen
	default:
		return t.primaryColor
	}
}

// SetFontSize updates the font size
func (t *CyberTheme) SetFontSize(size float32) {
	t.fontSize = size
}

// SetPrimaryColor updates the primary color
func (t *CyberTheme) SetPrimaryColor(c color.Color) {
	t.primaryColor = c
	t.textColor = c
	t.neonGreen = c
}

// SetSecondaryColor updates the secondary color
func (t *CyberTheme) SetSecondaryColor(c color.Color) {
	t.secondaryColor = c
	t.neonPink = c
}

// parseHexColor parses a hex color string to color.Color
func parseHexColor(hex string) (color.Color, error) {
	// Remove # if present
	if len(hex) > 0 && hex[0] == '#' {
		hex = hex[1:]
	}

	// Parse hex values
	if len(hex) != 6 {
		return nil, fmt.Errorf("invalid hex color format")
	}

	var r, g, b uint8
	if _, err := fmt.Sscanf(hex, "%02x%02x%02x", &r, &g, &b); err != nil {
		return nil, fmt.Errorf("failed to parse hex color: %w", err)
	}

	return color.NRGBA{R: r, G: g, B: b, A: 255}, nil
}

// ApplyToApp applies the cyberpunk theme to a Fyne app
func (t *CyberTheme) ApplyToApp(app fyne.App) {
	app.Settings().SetTheme(t)
}

// CreateGlowEffect creates a glow effect color for neon elements
func (t *CyberTheme) CreateGlowEffect(baseColor color.Color, intensity float32) color.Color {
	if nrgba, ok := baseColor.(color.NRGBA); ok {
		// Create a glow effect by adjusting alpha
		alpha := uint8(float32(nrgba.A) * intensity)
		return color.NRGBA{R: nrgba.R, G: nrgba.G, B: nrgba.B, A: alpha}
	}
	return baseColor
}

// GetTerminalColors returns colors specifically for terminal-like widgets
func (t *CyberTheme) GetTerminalColors() (background, text, cursor color.Color) {
	return color.NRGBA{R: 0, G: 0, B: 0, A: 255},        // Black background
		   t.terminalGreen,                                // Green text
		   color.NRGBA{R: 0, G: 255, B: 0, A: 200}        // Blinking cursor
}

// GetStatusColors returns a map of status level colors
func (t *CyberTheme) GetStatusColors() map[string]color.Color {
	return map[string]color.Color{
		"success":    t.successColor,
		"warning":    t.warningColor,
		"error":      t.errorColor,
		"info":       t.neonBlue,
		"processing": t.neonPink,
		"pending":    t.textSecondary,
	}
}

// GetStatusColors returns colors for different status indicators
func (t *CyberTheme) GetStatusColors() map[string]color.Color {
	return map[string]color.Color{
		"success":    t.successColor,
		"warning":    t.warningColor,
		"error":      t.errorColor,
		"info":       t.neonBlue,
		"processing": t.neonPink,
		"idle":       t.textSecondary,
	}
}
