// Package ai provides task planning capabilities
// planner.go - AI-powered task planning
package ai

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/tmc/langchaingo/llms"
)

// TaskPlanner handles AI-powered task planning
type TaskPlanner struct {
	llm    llms.Model
	logger *slog.Logger
}

// NewTaskPlanner creates a new task planner
func NewTaskPlanner(llm llms.Model) (*TaskPlanner, error) {
	if llm == nil {
		return nil, fmt.Errorf("LLM model is required")
	}

	return &TaskPlanner{
		llm:    llm,
		logger: slog.Default().With("component", "ai-planner"),
	}, nil
}

// CreatePlan creates a detailed plan for executing a task
func (tp *TaskPlanner) CreatePlan(description string, context map[string]interface{}) (*TaskPlan, error) {
	tp.logger.Info("Creating task plan", "description", description)

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// Build planning prompt
	prompt := tp.buildPlanningPrompt(description, context)

	// Generate plan using LLM
	response, err := llms.GenerateFromSinglePrompt(ctx, tp.llm, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to generate plan: %w", err)
	}

	// Parse response into structured plan
	plan := tp.parsePlanResponse(response, description)

	tp.logger.Info("Task plan created", "plan_id", plan.ID, "steps", len(plan.Steps))
	return plan, nil
}

// buildPlanningPrompt builds a prompt for task planning
func (tp *TaskPlanner) buildPlanningPrompt(description string, context map[string]interface{}) string {
	prompt := fmt.Sprintf(`You are an expert task planner for software development. 
Create a detailed, step-by-step plan to accomplish the following task:

Task: %s

Context: %v

Available tools and capabilities:
- Search: Find information using search engines
- Code Analysis: Analyze Go code for issues and improvements
- Database: Execute PostgreSQL queries and analyze performance
- Kubernetes: Manage K8s resources and cluster operations
- Docker: Manage containers, images, and Docker operations
- File Operations: Read, write, and analyze files
- Task Execution: Run build tasks, tests, and workflows
- Git Operations: Version control operations
- System Monitoring: Check system health and resources

Please create a plan with the following structure:
1. Break down the task into logical steps
2. For each step, specify:
   - Clear description of what needs to be done
   - Which tool(s) to use (if any)
   - Expected input and output
   - Dependencies on other steps
   - Estimated time to complete

3. Consider error handling and rollback strategies
4. Prioritize steps that can be done in parallel
5. Include validation and testing steps

Respond with a structured plan that can be executed systematically.`, description, context)

	return prompt
}

// parsePlanResponse parses the LLM response into a structured TaskPlan
func (tp *TaskPlanner) parsePlanResponse(response string, description string) *TaskPlan {
	// This is a simplified implementation
	// In a real implementation, you would parse the structured response more carefully
	
	plan := &TaskPlan{
		ID:          fmt.Sprintf("plan_%d", time.Now().Unix()),
		Description: description,
		Steps:       tp.extractStepsFromResponse(response),
		Estimated:   tp.estimateDuration(response),
		Created:     time.Now(),
	}

	return plan
}

// extractStepsFromResponse extracts task steps from the LLM response
func (tp *TaskPlanner) extractStepsFromResponse(response string) []TaskStep {
	// This is a simplified implementation
	// In a real implementation, you would use more sophisticated parsing
	
	steps := []TaskStep{
		{
			ID:          "step_1",
			Description: "Analyze task requirements and gather information",
			Tool:        "search",
			Input:       "task requirements analysis",
			Expected:    "Clear understanding of task scope and requirements",
			Dependencies: []string{},
			Metadata: map[string]interface{}{
				"priority": "high",
				"parallel": false,
			},
		},
		{
			ID:          "step_2",
			Description: "Plan implementation approach",
			Tool:        "",
			Input:       "requirements from step_1",
			Expected:    "Detailed implementation strategy",
			Dependencies: []string{"step_1"},
			Metadata: map[string]interface{}{
				"priority": "high",
				"parallel": false,
			},
		},
		{
			ID:          "step_3",
			Description: "Execute main task implementation",
			Tool:        "code_analysis",
			Input:       "implementation plan",
			Expected:    "Task completed successfully",
			Dependencies: []string{"step_2"},
			Metadata: map[string]interface{}{
				"priority": "medium",
				"parallel": true,
			},
		},
		{
			ID:          "step_4",
			Description: "Validate and test results",
			Tool:        "task",
			Input:       "implementation results",
			Expected:    "Validated and tested solution",
			Dependencies: []string{"step_3"},
			Metadata: map[string]interface{}{
				"priority": "high",
				"parallel": false,
			},
		},
	}

	return steps
}

// estimateDuration estimates the total duration for the plan
func (tp *TaskPlanner) estimateDuration(response string) time.Duration {
	// This is a simplified implementation
	// In a real implementation, you would analyze the complexity and estimate more accurately
	
	// Default estimation based on response length and complexity
	baseTime := 5 * time.Minute
	complexityFactor := len(response) / 1000 // Rough complexity based on response length
	
	if complexityFactor < 1 {
		complexityFactor = 1
	}
	
	return time.Duration(complexityFactor) * baseTime
}

// OptimizePlan optimizes a task plan for better execution
func (tp *TaskPlanner) OptimizePlan(plan *TaskPlan) (*TaskPlan, error) {
	tp.logger.Info("Optimizing task plan", "plan_id", plan.ID)

	// Analyze dependencies and identify parallel execution opportunities
	optimizedSteps := tp.optimizeStepOrder(plan.Steps)
	
	// Create optimized plan
	optimizedPlan := &TaskPlan{
		ID:          fmt.Sprintf("opt_%s", plan.ID),
		Description: plan.Description + " (optimized)",
		Steps:       optimizedSteps,
		Estimated:   tp.recalculateEstimation(optimizedSteps),
		Created:     time.Now(),
	}

	tp.logger.Info("Plan optimized", "original_steps", len(plan.Steps), "optimized_steps", len(optimizedSteps))
	return optimizedPlan, nil
}

// optimizeStepOrder optimizes the order of steps for better execution
func (tp *TaskPlanner) optimizeStepOrder(steps []TaskStep) []TaskStep {
	// This is a simplified implementation
	// In a real implementation, you would use graph algorithms to optimize dependencies
	
	optimized := make([]TaskStep, len(steps))
	copy(optimized, steps)
	
	// Mark steps that can run in parallel
	for i := range optimized {
		if canRunInParallel := tp.canStepRunInParallel(optimized[i], optimized); canRunInParallel {
			if optimized[i].Metadata == nil {
				optimized[i].Metadata = make(map[string]interface{})
			}
			optimized[i].Metadata["parallel"] = true
		}
	}
	
	return optimized
}

// canStepRunInParallel determines if a step can run in parallel with others
func (tp *TaskPlanner) canStepRunInParallel(step TaskStep, allSteps []TaskStep) bool {
	// Check if step has dependencies
	if len(step.Dependencies) > 0 {
		return false
	}
	
	// Check if other steps depend on this step
	for _, otherStep := range allSteps {
		for _, dep := range otherStep.Dependencies {
			if dep == step.ID {
				return false
			}
		}
	}
	
	return true
}

// recalculateEstimation recalculates the estimated duration for optimized steps
func (tp *TaskPlanner) recalculateEstimation(steps []TaskStep) time.Duration {
	// This is a simplified implementation
	// In a real implementation, you would consider parallel execution
	
	totalTime := time.Duration(0)
	parallelTime := time.Duration(0)
	
	for _, step := range steps {
		stepTime := 2 * time.Minute // Default step time
		
		if parallel, ok := step.Metadata["parallel"].(bool); ok && parallel {
			if stepTime > parallelTime {
				parallelTime = stepTime
			}
		} else {
			totalTime += stepTime
		}
	}
	
	return totalTime + parallelTime
}

// ValidatePlan validates a task plan for feasibility and correctness
func (tp *TaskPlanner) ValidatePlan(plan *TaskPlan) error {
	tp.logger.Info("Validating task plan", "plan_id", plan.ID)

	// Check for circular dependencies
	if err := tp.checkCircularDependencies(plan.Steps); err != nil {
		return fmt.Errorf("circular dependency detected: %w", err)
	}

	// Check for missing dependencies
	if err := tp.checkMissingDependencies(plan.Steps); err != nil {
		return fmt.Errorf("missing dependency detected: %w", err)
	}

	// Check for valid tools
	if err := tp.checkValidTools(plan.Steps); err != nil {
		return fmt.Errorf("invalid tool detected: %w", err)
	}

	tp.logger.Info("Plan validation completed", "plan_id", plan.ID)
	return nil
}

// checkCircularDependencies checks for circular dependencies in the plan
func (tp *TaskPlanner) checkCircularDependencies(steps []TaskStep) error {
	// This is a simplified implementation
	// In a real implementation, you would use graph algorithms to detect cycles
	
	visited := make(map[string]bool)
	recursionStack := make(map[string]bool)
	
	for _, step := range steps {
		if !visited[step.ID] {
			if tp.hasCycle(step.ID, steps, visited, recursionStack) {
				return fmt.Errorf("circular dependency involving step %s", step.ID)
			}
		}
	}
	
	return nil
}

// hasCycle checks if there's a cycle starting from a given step
func (tp *TaskPlanner) hasCycle(stepID string, steps []TaskStep, visited, recursionStack map[string]bool) bool {
	visited[stepID] = true
	recursionStack[stepID] = true
	
	// Find the step
	var currentStep *TaskStep
	for _, step := range steps {
		if step.ID == stepID {
			currentStep = &step
			break
		}
	}
	
	if currentStep == nil {
		return false
	}
	
	// Check dependencies
	for _, dep := range currentStep.Dependencies {
		if !visited[dep] {
			if tp.hasCycle(dep, steps, visited, recursionStack) {
				return true
			}
		} else if recursionStack[dep] {
			return true
		}
	}
	
	recursionStack[stepID] = false
	return false
}

// checkMissingDependencies checks for missing dependencies
func (tp *TaskPlanner) checkMissingDependencies(steps []TaskStep) error {
	stepIDs := make(map[string]bool)
	for _, step := range steps {
		stepIDs[step.ID] = true
	}
	
	for _, step := range steps {
		for _, dep := range step.Dependencies {
			if !stepIDs[dep] {
				return fmt.Errorf("step %s depends on missing step %s", step.ID, dep)
			}
		}
	}
	
	return nil
}

// checkValidTools checks if all specified tools are valid
func (tp *TaskPlanner) checkValidTools(steps []TaskStep) error {
	validTools := map[string]bool{
		"search": true, "code_analysis": true, "database_query": true,
		"kubernetes": true, "docker": true, "file": true, "task": true,
		"git": true, "cloudflare": true, "system": true,
	}
	
	for _, step := range steps {
		if step.Tool != "" && !validTools[step.Tool] {
			return fmt.Errorf("step %s uses invalid tool %s", step.ID, step.Tool)
		}
	}
	
	return nil
}

// TODO: Implement machine learning for plan optimization
// TODO: Implement plan templates and reusable patterns
// TODO: Implement plan execution monitoring and adaptation
// TODO: Implement plan cost estimation and resource planning
// TODO: Implement collaborative planning with multiple agents
// TODO: Implement plan versioning and rollback capabilities
