// Package k8s provides Kubernetes resource management functionality
// resources.go - Handles pod, service, deployment operations
package k8s

import (
	"context"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ResourceInfo represents information about a Kubernetes resource
type ResourceInfo struct {
	Kind      string
	Name      string
	Namespace string
	Status    string
	Age       time.Duration
	Labels    map[string]string
}

// ResourceManager handles Kubernetes resource operations
type ResourceManager struct {
	clusterManager *ClusterManager
}

// NewResourceManager creates a new resource manager
func NewResourceManager(clusterManager *ClusterManager) *ResourceManager {
	return &ResourceManager{
		clusterManager: clusterManager,
	}
}

// ListPods lists pods in the specified namespace
func (rm *ResourceManager) ListPods(namespace string) ([]ResourceInfo, error) {
	client, err := rm.clusterManager.GetActiveCluster()
	if err != nil {
		return nil, err
	}

	return client.ListPods(namespace)
}

// ListServices lists services in the specified namespace
func (rm *ResourceManager) ListServices(namespace string) ([]ResourceInfo, error) {
	client, err := rm.clusterManager.GetActiveCluster()
	if err != nil {
		return nil, err
	}

	return client.ListServices(namespace)
}

// ListDeployments lists deployments in the specified namespace
func (rm *ResourceManager) ListDeployments(namespace string) ([]ResourceInfo, error) {
	client, err := rm.clusterManager.GetActiveCluster()
	if err != nil {
		return nil, err
	}

	return client.ListDeployments(namespace)
}

// GetPodLogs retrieves logs from a specific pod
func (rm *ResourceManager) GetPodLogs(namespace, podName string, lines int64) (string, error) {
	client, err := rm.clusterManager.GetActiveCluster()
	if err != nil {
		return "", err
	}

	return client.GetPodLogs(namespace, podName, lines)
}

// DeletePod deletes a specific pod
func (rm *ResourceManager) DeletePod(namespace, podName string) error {
	client, err := rm.clusterManager.GetActiveCluster()
	if err != nil {
		return err
	}

	return client.DeletePod(namespace, podName)
}

// ScaleDeployment scales a deployment to the specified number of replicas
func (rm *ResourceManager) ScaleDeployment(namespace, deploymentName string, replicas int32) error {
	client, err := rm.clusterManager.GetActiveCluster()
	if err != nil {
		return err
	}

	return client.ScaleDeployment(namespace, deploymentName, replicas)
}

// ListPods lists pods in the specified namespace
func (c *ClusterClient) ListPods(namespace string) ([]ResourceInfo, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pods, err := client.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	resources := make([]ResourceInfo, len(pods.Items))
	for i, pod := range pods.Items {
		resources[i] = ResourceInfo{
			Kind:      "Pod",
			Name:      pod.Name,
			Namespace: pod.Namespace,
			Status:    string(pod.Status.Phase),
			Age:       time.Since(pod.CreationTimestamp.Time),
			Labels:    pod.Labels,
		}
	}

	return resources, nil
}

// ListServices lists services in the specified namespace
func (c *ClusterClient) ListServices(namespace string) ([]ResourceInfo, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	services, err := client.CoreV1().Services(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list services: %w", err)
	}

	resources := make([]ResourceInfo, len(services.Items))
	for i, svc := range services.Items {
		resources[i] = ResourceInfo{
			Kind:      "Service",
			Name:      svc.Name,
			Namespace: svc.Namespace,
			Status:    "Active", // Services don't have a status phase
			Age:       time.Since(svc.CreationTimestamp.Time),
			Labels:    svc.Labels,
		}
	}

	return resources, nil
}

// ListDeployments lists deployments in the specified namespace
func (c *ClusterClient) ListDeployments(namespace string) ([]ResourceInfo, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	deployments, err := client.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list deployments: %w", err)
	}

	resources := make([]ResourceInfo, len(deployments.Items))
	for i, deploy := range deployments.Items {
		status := fmt.Sprintf("%d/%d", deploy.Status.ReadyReplicas, deploy.Status.Replicas)
		resources[i] = ResourceInfo{
			Kind:      "Deployment",
			Name:      deploy.Name,
			Namespace: deploy.Namespace,
			Status:    status,
			Age:       time.Since(deploy.CreationTimestamp.Time),
			Labels:    deploy.Labels,
		}
	}

	return resources, nil
}

// GetPodLogs retrieves logs from a specific pod
func (c *ClusterClient) GetPodLogs(namespace, podName string, lines int64) (string, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return "", fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req := client.CoreV1().Pods(namespace).GetLogs(podName, &metav1.PodLogOptions{
		TailLines: &lines,
	})

	logs, err := req.Stream(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get pod logs: %w", err)
	}
	defer logs.Close()

	buf := make([]byte, 1024*1024) // 1MB buffer
	n, err := logs.Read(buf)
	if err != nil && err.Error() != "EOF" {
		return "", fmt.Errorf("failed to read pod logs: %w", err)
	}

	return string(buf[:n]), nil
}

// DeletePod deletes a specific pod
func (c *ClusterClient) DeletePod(namespace, podName string) error {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := client.CoreV1().Pods(namespace).Delete(ctx, podName, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete pod: %w", err)
	}

	return nil
}

// ScaleDeployment scales a deployment to the specified number of replicas
func (c *ClusterClient) ScaleDeployment(namespace, deploymentName string, replicas int32) error {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get the deployment
	deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, deploymentName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get deployment: %w", err)
	}

	// Update replica count
	deployment.Spec.Replicas = &replicas

	// Update the deployment
	_, err = client.AppsV1().Deployments(namespace).Update(ctx, deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("failed to scale deployment: %w", err)
	}

	return nil
}

// GetResourcesByLabel gets resources by label selector
func (rm *ResourceManager) GetResourcesByLabel(namespace, labelSelector string) ([]ResourceInfo, error) {
	client, err := rm.clusterManager.GetActiveCluster()
	if err != nil {
		return nil, err
	}

	c := client
	c.mu.RLock()
	k8sClient := c.Client
	c.mu.RUnlock()

	if k8sClient == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get pods with label selector
	pods, err := k8sClient.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods by label: %w", err)
	}

	resources := make([]ResourceInfo, len(pods.Items))
	for i, pod := range pods.Items {
		resources[i] = ResourceInfo{
			Kind:      "Pod",
			Name:      pod.Name,
			Namespace: pod.Namespace,
			Status:    string(pod.Status.Phase),
			Age:       time.Since(pod.CreationTimestamp.Time),
			Labels:    pod.Labels,
		}
	}

	return resources, nil
}
