// Package k8s provides Kubernetes cluster management functionality
// Implements configurable connections with graceful fallback behavior
package k8s

import (
	"context"
	"fmt"
	"log/slog"
	"path/filepath"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"

	"assistant-go/internal/types"
	"assistant-go/internal/config"
)

// Manager handles Kubernetes operations using domain-specific managers
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// Domain-specific managers
	clusterManager  *ClusterManager
	resourceManager *ResourceManager

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
}

// ClusterClient represents a Kubernetes cluster connection
type ClusterClient struct {
	Name       string
	Config     config.K8sContext
	Client     kubernetes.Interface
	RestConfig *rest.Config
	Status     ClusterStatus
	mu         sync.RWMutex
}

// ClusterStatus represents the status of a cluster connection
type ClusterStatus struct {
	Connected   bool
	Error       error
	LastCheck   time.Time
	Version     string
	NodeCount   int
	Namespaces  []string
}

// ResourceInfo represents information about Kubernetes resources
type ResourceInfo struct {
	Kind      string
	Name      string
	Namespace string
	Status    string
	Age       time.Duration
	Labels    map[string]string
}

// NewManager creates a new Kubernetes manager with domain-specific structure
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	clusterManager := NewClusterManager()
	resourceManager := NewResourceManager(clusterManager)

	return &Manager{
		clusterManager:  clusterManager,
		resourceManager: resourceManager,
		ctx:             ctx,
		cancel:          cancel,
		logger:          slog.Default().With("component", "k8s"),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.config = cfg
	m.logger.Info("Initializing Kubernetes manager")

	// Initialize configured contexts
	for _, contextCfg := range cfg.K8s.Contexts {
		if contextCfg.Enabled {
			if err := m.AddContext(contextCfg); err != nil {
				m.logger.Error("Failed to add context", "name", contextCfg.Name, "error", err)
				// Continue with other contexts - graceful fallback
			}
		}
	}

	// Set default active context
	if m.config.K8s.DefaultContext != "" {
		m.activeContext = m.config.K8s.DefaultContext
	}

	m.logger.Info("Kubernetes manager initialized", "contexts", len(m.clients))
	return nil
}

// Start implements the Module interface
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("kubernetes manager is already running")
	}

	m.logger.Info("Starting Kubernetes manager")

	// Start cluster monitoring
	go m.monitorClusters()

	m.running = true
	m.logger.Info("Kubernetes manager started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping Kubernetes manager")

	// Cancel context to stop monitoring
	m.cancel()

	m.running = false
	m.logger.Info("Kubernetes manager stopped")
	return nil
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "kubernetes"
}

// Health implements the Module interface
func (m *Manager) Health() types.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := types.ModuleHealth{
		Status:    "healthy",
		Message:   fmt.Sprintf("%d contexts configured", len(m.clients)),
		LastCheck: time.Now(),
	}

	// Check if any contexts are unhealthy
	unhealthyCount := 0
	for _, client := range m.clients {
		status := client.GetStatus()
		if !status.Connected {
			unhealthyCount++
		}
	}

	if unhealthyCount > 0 {
		health.Status = "degraded"
		health.Message = fmt.Sprintf("%d of %d contexts unhealthy", unhealthyCount, len(m.clients))
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
	}

	if m.activeContext != "" {
		health.Message += fmt.Sprintf(" (active: %s)", m.activeContext)
	}

	return health
}

// AddContext adds a new Kubernetes context
func (m *Manager) AddContext(cfg config.K8sContext) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[cfg.Name]; exists {
		return fmt.Errorf("context %s already exists", cfg.Name)
	}

	client := &ClusterClient{
		Name:   cfg.Name,
		Config: cfg,
		Status: ClusterStatus{
			Connected: false,
			LastCheck: time.Now(),
		},
	}

	// Attempt to connect
	if err := client.Connect(); err != nil {
		m.logger.Error("Failed to connect to cluster", "name", cfg.Name, "error", err)
		// Store client even if it failed - user can retry
		client.Status.Error = err
	}

	m.clients[cfg.Name] = client
	m.logger.Info("Kubernetes context added", "name", cfg.Name, "connected", client.Status.Connected)

	return nil
}

// RemoveContext removes a Kubernetes context
func (m *Manager) RemoveContext(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[name]; !exists {
		return fmt.Errorf("context %s does not exist", name)
	}

	delete(m.clients, name)

	// Update active context if it was removed
	if m.activeContext == name {
		m.activeContext = ""
		// Set to first available context
		for contextName := range m.clients {
			m.activeContext = contextName
			break
		}
	}

	m.logger.Info("Kubernetes context removed", "name", name)
	return nil
}

// GetContext returns a Kubernetes context by name
func (m *Manager) GetContext(name string) (*ClusterClient, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	client, exists := m.clients[name]
	if !exists {
		return nil, fmt.Errorf("context %s does not exist", name)
	}

	return client, nil
}

// ListContexts returns all Kubernetes contexts
func (m *Manager) ListContexts() map[string]*ClusterClient {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Return a copy to prevent external modification
	contexts := make(map[string]*ClusterClient)
	for name, client := range m.clients {
		contexts[name] = client
	}

	return contexts
}

// SetActiveContext sets the active Kubernetes context
func (m *Manager) SetActiveContext(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[name]; !exists {
		return fmt.Errorf("context %s does not exist", name)
	}

	m.activeContext = name
	m.logger.Info("Active context changed", "context", name)
	return nil
}

// GetActiveContext returns the active Kubernetes context
func (m *Manager) GetActiveContext() (*ClusterClient, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.activeContext == "" {
		return nil, fmt.Errorf("no active context set")
	}

	client, exists := m.clients[m.activeContext]
	if !exists {
		return nil, fmt.Errorf("active context %s does not exist", m.activeContext)
	}

	return client, nil
}

// ListPods lists pods in the specified namespace
func (m *Manager) ListPods(namespace string) ([]ResourceInfo, error) {
	client, err := m.GetActiveContext()
	if err != nil {
		return nil, err
	}

	return client.ListPods(namespace)
}

// ListServices lists services in the specified namespace
func (m *Manager) ListServices(namespace string) ([]ResourceInfo, error) {
	client, err := m.GetActiveContext()
	if err != nil {
		return nil, err
	}

	return client.ListServices(namespace)
}

// ListNamespaces lists all namespaces
func (m *Manager) ListNamespaces() ([]string, error) {
	client, err := m.GetActiveContext()
	if err != nil {
		return nil, err
	}

	return client.ListNamespaces()
}

// monitorClusters monitors the health of all cluster connections
func (m *Manager) monitorClusters() {
	ticker := time.NewTicker(m.config.K8s.RefreshRate)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkClusterHealth()
		}
	}
}

// checkClusterHealth checks the health of all cluster connections
func (m *Manager) checkClusterHealth() {
	m.mu.RLock()
	clients := make([]*ClusterClient, 0, len(m.clients))
	for _, client := range m.clients {
		clients = append(clients, client)
	}
	m.mu.RUnlock()

	for _, client := range clients {
		go client.CheckHealth()
	}
}

// Connect establishes a connection to the Kubernetes cluster
func (c *ClusterClient) Connect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Build config path
	configPath := c.Config.ConfigPath
	if configPath == "~/.kube/config" {
		if home := homedir.HomeDir(); home != "" {
			configPath = filepath.Join(home, ".kube", "config")
		}
	}

	// Load kubeconfig
	config, err := clientcmd.BuildConfigFromFlags("", configPath)
	if err != nil {
		// Try in-cluster config as fallback
		config, err = rest.InClusterConfig()
		if err != nil {
			c.Status.Error = err
			c.Status.Connected = false
			return fmt.Errorf("failed to load kubeconfig: %w", err)
		}
	}

	// Create client
	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		c.Status.Error = err
		c.Status.Connected = false
		return fmt.Errorf("failed to create kubernetes client: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err = client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{Limit: 1})
	if err != nil {
		c.Status.Error = err
		c.Status.Connected = false
		return fmt.Errorf("failed to connect to cluster: %w", err)
	}

	c.Client = client
	c.RestConfig = config
	c.Status.Connected = true
	c.Status.Error = nil
	c.Status.LastCheck = time.Now()

	// Get cluster info
	c.updateClusterInfo()

	return nil
}

// CheckHealth checks the health of the cluster connection
func (c *ClusterClient) CheckHealth() {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		c.mu.Lock()
		c.Status.Connected = false
		c.Status.Error = fmt.Errorf("client not initialized")
		c.Status.LastCheck = time.Now()
		c.mu.Unlock()
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{Limit: 1})

	c.mu.Lock()
	c.Status.Connected = (err == nil)
	c.Status.Error = err
	c.Status.LastCheck = time.Now()
	if err == nil {
		c.updateClusterInfo()
	}
	c.mu.Unlock()
}

// updateClusterInfo updates cluster information
func (c *ClusterClient) updateClusterInfo() {
	if c.Client == nil {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get server version
	version, err := c.Client.Discovery().ServerVersion()
	if err == nil {
		c.Status.Version = version.String()
	}

	// Get node count
	nodes, err := c.Client.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err == nil {
		c.Status.NodeCount = len(nodes.Items)
	}

	// Get namespaces
	namespaces, err := c.Client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err == nil {
		nsNames := make([]string, len(namespaces.Items))
		for i, ns := range namespaces.Items {
			nsNames[i] = ns.Name
		}
		c.Status.Namespaces = nsNames
	}
}

// GetStatus returns the current cluster status
func (c *ClusterClient) GetStatus() ClusterStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.Status
}

// ListPods lists pods in the specified namespace
func (c *ClusterClient) ListPods(namespace string) ([]ResourceInfo, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pods, err := client.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	resources := make([]ResourceInfo, len(pods.Items))
	for i, pod := range pods.Items {
		resources[i] = ResourceInfo{
			Kind:      "Pod",
			Name:      pod.Name,
			Namespace: pod.Namespace,
			Status:    string(pod.Status.Phase),
			Age:       time.Since(pod.CreationTimestamp.Time),
			Labels:    pod.Labels,
		}
	}

	return resources, nil
}

// ListServices lists services in the specified namespace
func (c *ClusterClient) ListServices(namespace string) ([]ResourceInfo, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	services, err := client.CoreV1().Services(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list services: %w", err)
	}

	resources := make([]ResourceInfo, len(services.Items))
	for i, svc := range services.Items {
		resources[i] = ResourceInfo{
			Kind:      "Service",
			Name:      svc.Name,
			Namespace: svc.Namespace,
			Status:    "Active", // Services don't have a status phase
			Age:       time.Since(svc.CreationTimestamp.Time),
			Labels:    svc.Labels,
		}
	}

	return resources, nil
}

// ListNamespaces lists all namespaces
func (c *ClusterClient) ListNamespaces() ([]string, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	namespaces, err := client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list namespaces: %w", err)
	}

	names := make([]string, len(namespaces.Items))
	for i, ns := range namespaces.Items {
		names[i] = ns.Name
	}

	return names, nil
}
