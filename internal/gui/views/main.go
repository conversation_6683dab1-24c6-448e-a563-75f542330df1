// Package views provides GUI view components for Assistant-Go
// Implements the cyberpunk aesthetic with proper scrolling and responsive design
package views

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// MainView represents the main content area of the application
type MainView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject
}

// NewMainView creates a new main view with cyberpunk styling
func NewMainView(cfg *config.Config, theme *cybertheme.CyberTheme) (*MainView, error) {
	// Create the main content container with scrolling support
	// This fixes the content overflow issues mentioned in requirements
	mainContainer := container.NewVBox()
	
	// Create scrollable container to handle content overflow
	scrollContainer := container.NewScroll(mainContainer)
	scrollContainer.SetMinSize(fyne.NewSize(800, 600))
	
	// Create welcome content with cyberpunk styling
	welcomeContent := createWelcomeContent(cfg, theme)
	mainContainer.Add(welcomeContent)
	
	return &MainView{
		config:    cfg,
		theme:     theme,
		container: scrollContainer,
		content:   welcomeContent,
	}, nil
}

// createWelcomeContent creates the initial welcome screen
func createWelcomeContent(cfg *config.Config, theme *cybertheme.CyberTheme) fyne.CanvasObject {
	// Create cyberpunk-styled welcome message
	welcomeText := widget.NewRichTextFromMarkdown(`
# 🖥️ Assistant-Go Development Environment

**STATUS:** SYSTEM ONLINE  
**VERSION:** ` + cfg.App.Version + `  
**ENVIRONMENT:** ` + cfg.App.Environment + `  

> Welcome to your cyberpunk development assistant.
> All systems are operational and ready for your commands.

## Available Modules:

### 🗄️ Database Module
- **PostgreSQL Navigator** - Intelligent query optimization and visual explain plans
- **Connection Management** - Multiple database connections with auto-pooling
- **AI-Powered Analysis** - Smart error explanations and performance suggestions

### ☸️ Kubernetes Module  
- **Cluster Commander** - Visual cluster management and real-time monitoring
- **Resource Browser** - Intuitive navigation of namespaces, pods, and services
- **Deployment Tools** - Drag-and-drop resource management

### 📋 Task Module
- **Makefile Integration** - Direct access to your build tasks
- **Taskfile Support** - Modern task runner integration
- **Favorites Management** - Quick access to frequently used commands

### 🤖 MCP Module (Future)
- **Model Context Protocol** - Visualize AI interactions in real-time
- **Context Flow** - Debug and understand AI tool calls
- **Response Analysis** - Monitor model responses and context usage

## Getting Started:

1. **Configure Connections** - Set up your database and Kubernetes connections
2. **Select a Module** - Choose from the sidebar to begin working
3. **Explore Features** - Each module provides comprehensive development tools

---

**>>> READY FOR DEVELOPMENT <<<**
`)

	// Create status indicators
	statusContainer := container.NewHBox(
		createStatusIndicator("Database", "Disconnected", "warning", theme),
		createStatusIndicator("Kubernetes", "Disconnected", "warning", theme),
		createStatusIndicator("Tasks", "Ready", "success", theme),
		createStatusIndicator("MCP", "Pending", "info", theme),
	)

	// Create action buttons
	actionContainer := container.NewHBox(
		widget.NewButton("Configure Database", func() {
			// TODO: Open database configuration
		}),
		widget.NewButton("Setup Kubernetes", func() {
			// TODO: Open K8s configuration
		}),
		widget.NewButton("View Tasks", func() {
			// TODO: Switch to tasks module
		}),
	)

	// Combine all elements
	return container.NewVBox(
		welcomeText,
		widget.NewSeparator(),
		widget.NewLabel("System Status:"),
		statusContainer,
		widget.NewSeparator(),
		widget.NewLabel("Quick Actions:"),
		actionContainer,
		widget.NewSeparator(),
		widget.NewLabel(">>> CYBERPUNK DEVELOPMENT ENVIRONMENT ACTIVE <<<"),
	)
}

// createStatusIndicator creates a status indicator with cyberpunk styling
func createStatusIndicator(name, status, level string, theme *cybertheme.CyberTheme) fyne.CanvasObject {
	// Create status label with appropriate color
	statusLabel := widget.NewLabel(status)
	
	// Apply color based on level
	statusColors := theme.GetStatusColors()
	if color, exists := statusColors[level]; exists {
		// TODO: Apply color to label when Fyne supports it
		_ = color
	}
	
	return container.NewVBox(
		widget.NewLabel(name),
		statusLabel,
	)
}

// Content returns the main view's content
func (mv *MainView) Content() fyne.CanvasObject {
	return mv.container
}

// SetContent updates the main view's content
func (mv *MainView) SetContent(content fyne.CanvasObject) {
	// Clear existing content
	mv.container.Content = container.NewVBox()
	
	// Add new content to a scrollable container
	if content != nil {
		mv.container.Content = container.NewVBox(content)
		mv.content = content
	}
	
	// Refresh the container
	mv.container.Refresh()
}

// Refresh refreshes the main view
func (mv *MainView) Refresh() {
	if mv.container != nil {
		mv.container.Refresh()
	}
}

// GetCurrentContent returns the current content
func (mv *MainView) GetCurrentContent() fyne.CanvasObject {
	return mv.content
}

// SetMinSize sets the minimum size of the main view
func (mv *MainView) SetMinSize(size fyne.Size) {
	mv.container.SetMinSize(size)
}

// ShowLoading displays a loading indicator
func (mv *MainView) ShowLoading(message string) {
	loadingContent := container.NewVBox(
		widget.NewProgressBarInfinite(),
		widget.NewLabel(message),
		widget.NewLabel(">>> PROCESSING <<<"),
	)
	
	mv.SetContent(loadingContent)
}

// ShowError displays an error message with cyberpunk styling
func (mv *MainView) ShowError(title, message string) {
	errorContent := container.NewVBox(
		widget.NewLabel("🚨 ERROR DETECTED 🚨"),
		widget.NewSeparator(),
		widget.NewLabel("ERROR: "+title),
		widget.NewLabel(message),
		widget.NewSeparator(),
		widget.NewLabel(">>> SYSTEM ALERT <<<"),
	)
	
	mv.SetContent(errorContent)
}

// ShowSuccess displays a success message
func (mv *MainView) ShowSuccess(title, message string) {
	successContent := container.NewVBox(
		widget.NewLabel("✅ OPERATION SUCCESSFUL ✅"),
		widget.NewSeparator(),
		widget.NewLabel("SUCCESS: "+title),
		widget.NewLabel(message),
		widget.NewSeparator(),
		widget.NewLabel(">>> TASK COMPLETED <<<"),
	)
	
	mv.SetContent(successContent)
}
