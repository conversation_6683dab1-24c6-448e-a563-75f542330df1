// Package tasks provides the UI components for task management
// Implements cyberpunk-styled task interface with real-time execution
package tasks

import (
	"fmt"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI provides the user interface for task management
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme
	
	// Main components
	content       *container.Scroll
	taskList      *widget.List
	favoritesList *widget.List
	outputText    *widget.Entry
	statusLabel   *widget.Label
	
	// Current state
	selectedTask string
	allTasks     map[string]*Task
	favorites    []string
}

// NewUI creates a new task management UI
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager: manager,
		config:  cfg,
		theme:   theme,
	}
	
	ui.createComponents()
	ui.refreshTasks()
	return ui
}

// createComponents creates all UI components
func (ui *UI) createComponents() {
	// Create task list
	ui.createTaskList()
	
	// Create favorites list
	ui.createFavoritesList()
	
	// Create output display
	ui.createOutputDisplay()
	
	// Create status label
	ui.statusLabel = widget.NewLabel(">>> TASK MODULE READY <<<")
	
	// Create main layout
	ui.createMainLayout()
}

// createTaskList creates the main task list
func (ui *UI) createTaskList() {
	ui.taskList = widget.NewList(
		func() int {
			return len(ui.allTasks)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add task type icon
				widget.NewLabel("Task Name"),
				widget.NewLabel("Source"),
				widget.NewLabel("Description"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			tasks := ui.getTaskSlice()
			if id < len(tasks) {
				task := tasks[id]
				
				container := obj.(*container.Container)
				nameLabel := container.Objects[1].(*widget.Label)
				sourceLabel := container.Objects[2].(*widget.Label)
				descLabel := container.Objects[3].(*widget.Label)
				
				nameLabel.SetText(task.Name)
				sourceLabel.SetText(task.Source)
				
				description := task.Description
				if description == "" {
					description = "No description"
				}
				if len(description) > 50 {
					description = description[:50] + "..."
				}
				descLabel.SetText(description)
			}
		},
	)
	
	ui.taskList.OnSelected = func(id widget.ListItemID) {
		tasks := ui.getTaskSlice()
		if id < len(tasks) {
			ui.selectedTask = tasks[id].Name
			ui.updateStatus(fmt.Sprintf("Selected task: %s", ui.selectedTask))
		}
	}
}

// createFavoritesList creates the favorites list
func (ui *UI) createFavoritesList() {
	ui.favoritesList = widget.NewList(
		func() int {
			return len(ui.favorites)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add favorite icon
				widget.NewLabel("Favorite Task"),
				widget.NewButton("Run", nil),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(ui.favorites) {
				taskName := ui.favorites[id]
				
				container := obj.(*container.Container)
				nameLabel := container.Objects[1].(*widget.Label)
				runButton := container.Objects[2].(*widget.Button)
				
				nameLabel.SetText(taskName)
				runButton.OnTapped = func() {
					ui.executeTask(taskName)
				}
			}
		},
	)
}

// createOutputDisplay creates the output display area
func (ui *UI) createOutputDisplay() {
	ui.outputText = widget.NewMultiLineEntry()
	ui.outputText.SetPlaceHolder("Task output will appear here...")
	ui.outputText.Disable() // Read-only
	ui.outputText.Resize(fyne.NewSize(600, 300))
}

// createMainLayout creates the main UI layout
func (ui *UI) createMainLayout() {
	// Create favorites section
	favoritesSection := container.NewVBox(
		widget.NewLabel("⭐ Favorite Tasks"),
		widget.NewSeparator(),
		ui.favoritesList,
		container.NewHBox(
			widget.NewButton("Add to Favorites", ui.addToFavorites),
			widget.NewButton("Remove from Favorites", ui.removeFromFavorites),
		),
	)
	
	// Create all tasks section
	tasksSection := container.NewVBox(
		widget.NewLabel("📋 All Tasks"),
		widget.NewSeparator(),
		ui.taskList,
		container.NewHBox(
			widget.NewButton("Execute Task", ui.executeSelectedTask),
			widget.NewButton("Show Details", ui.showTaskDetails),
			widget.NewButton("Refresh", ui.refreshTasks),
		),
	)
	
	// Create output section
	outputSection := container.NewVBox(
		widget.NewLabel("📄 Task Output"),
		widget.NewSeparator(),
		container.NewScroll(ui.outputText),
		container.NewHBox(
			widget.NewButton("Clear Output", func() { ui.outputText.SetText("") }),
			widget.NewButton("Save Output", ui.saveOutput),
		),
	)
	
	// Create main container with scrolling support
	mainContainer := container.NewVBox(
		widget.NewRichTextFromMarkdown(`
# 📋 Task Manager

**MAKEFILE & TASKFILE INTEGRATION**

> Direct access to your build tasks with intelligent management.
> Supports both Makefile and Taskfile with favorites and real-time execution.

---
`),
		favoritesSection,
		widget.NewSeparator(),
		tasksSection,
		widget.NewSeparator(),
		outputSection,
		widget.NewSeparator(),
		ui.statusLabel,
	)
	
	// Wrap in scroll container to handle overflow
	ui.content = container.NewScroll(mainContainer)
	ui.content.SetMinSize(fyne.NewSize(800, 600))
}

// refreshTasks refreshes the task lists
func (ui *UI) refreshTasks() {
	// Reload tasks from manager
	ui.manager.ReloadTasks()
	
	// Update local state
	ui.allTasks = ui.manager.GetAllTasks()
	ui.favorites = ui.manager.GetFavorites()
	
	// Refresh UI components
	ui.taskList.Refresh()
	ui.favoritesList.Refresh()
	
	ui.updateStatus(fmt.Sprintf("Loaded %d tasks (%d favorites)", 
		len(ui.allTasks), len(ui.favorites)))
}

// getTaskSlice returns tasks as a slice for list display
func (ui *UI) getTaskSlice() []*Task {
	tasks := make([]*Task, 0, len(ui.allTasks))
	for _, task := range ui.allTasks {
		tasks = append(tasks, task)
	}
	return tasks
}

// executeSelectedTask executes the currently selected task
func (ui *UI) executeSelectedTask() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}
	
	ui.executeTask(ui.selectedTask)
}

// executeTask executes a specific task
func (ui *UI) executeTask(taskName string) {
	ui.updateStatus(fmt.Sprintf("Executing task: %s", taskName))
	ui.outputText.SetText(fmt.Sprintf(">>> Executing task: %s <<<\n", taskName))
	
	// Execute task in background
	go func() {
		execution, err := ui.manager.ExecuteTask(taskName)
		if err != nil {
			ui.updateStatus(fmt.Sprintf("Error executing task: %s", err.Error()))
			ui.appendOutput(fmt.Sprintf("Error: %s\n", err.Error()))
			return
		}
		
		// Update output
		ui.appendOutput(fmt.Sprintf("Task: %s\n", execution.Task.Name))
		ui.appendOutput(fmt.Sprintf("Duration: %v\n", execution.Duration))
		ui.appendOutput(fmt.Sprintf("Exit Code: %d\n", execution.ExitCode))
		ui.appendOutput("--- Output ---\n")
		ui.appendOutput(execution.Output)
		ui.appendOutput("\n--- End Output ---\n")
		
		if execution.Error != nil {
			ui.updateStatus(fmt.Sprintf("Task failed: %s (exit code: %d)", 
				taskName, execution.ExitCode))
		} else {
			ui.updateStatus(fmt.Sprintf("Task completed: %s (%v)", 
				taskName, execution.Duration))
		}
	}()
}

// appendOutput appends text to the output display
func (ui *UI) appendOutput(text string) {
	currentText := ui.outputText.Text
	ui.outputText.SetText(currentText + text)
	
	// Scroll to bottom
	ui.outputText.CursorRow = strings.Count(ui.outputText.Text, "\n")
}

// addToFavorites adds the selected task to favorites
func (ui *UI) addToFavorites() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}
	
	if err := ui.manager.AddFavorite(ui.selectedTask); err != nil {
		ui.updateStatus(fmt.Sprintf("Error adding to favorites: %s", err.Error()))
		return
	}
	
	ui.refreshTasks()
	ui.updateStatus(fmt.Sprintf("Added '%s' to favorites", ui.selectedTask))
}

// removeFromFavorites removes the selected task from favorites
func (ui *UI) removeFromFavorites() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}
	
	if err := ui.manager.RemoveFavorite(ui.selectedTask); err != nil {
		ui.updateStatus(fmt.Sprintf("Error removing from favorites: %s", err.Error()))
		return
	}
	
	ui.refreshTasks()
	ui.updateStatus(fmt.Sprintf("Removed '%s' from favorites", ui.selectedTask))
}

// showTaskDetails shows detailed information about the selected task
func (ui *UI) showTaskDetails() {
	if ui.selectedTask == "" {
		ui.updateStatus("Error: No task selected")
		return
	}
	
	task, err := ui.manager.GetTask(ui.selectedTask)
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error getting task details: %s", err.Error()))
		return
	}
	
	// Create details content
	details := fmt.Sprintf(`
**Task Details**

**Name:** %s
**Source:** %s
**Command:** %s
**Description:** %s
**Dependencies:** %s
**Last Run:** %s
**Run Count:** %d
`, 
		task.Name,
		task.Source,
		task.Command,
		task.Description,
		strings.Join(task.Dependencies, ", "),
		formatTime(task.LastRun),
		task.RunCount,
	)
	
	detailsText := widget.NewRichTextFromMarkdown(details)
	
	// Show dialog
	dialog := dialog.NewCustom("Task Details", "Close", 
		container.NewScroll(detailsText), 
		fyne.CurrentApp().Driver().AllWindows()[0])
	dialog.Resize(fyne.NewSize(500, 400))
	dialog.Show()
}

// saveOutput saves the current output to a file
func (ui *UI) saveOutput() {
	if ui.outputText.Text == "" {
		ui.updateStatus("No output to save")
		return
	}
	
	// Create save dialog
	dialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
		if err != nil {
			ui.updateStatus(fmt.Sprintf("Error saving file: %s", err.Error()))
			return
		}
		if writer == nil {
			return // User cancelled
		}
		defer writer.Close()
		
		if _, err := writer.Write([]byte(ui.outputText.Text)); err != nil {
			ui.updateStatus(fmt.Sprintf("Error writing file: %s", err.Error()))
			return
		}
		
		ui.updateStatus("Output saved successfully")
	}, fyne.CurrentApp().Driver().AllWindows()[0])
	
	dialog.SetFileName(fmt.Sprintf("task_output_%s.txt", 
		time.Now().Format("20060102_150405")))
	dialog.Show()
}

// updateStatus updates the status label
func (ui *UI) updateStatus(message string) {
	ui.statusLabel.SetText(fmt.Sprintf(">>> %s <<<", message))
}

// formatTime formats a time for display
func formatTime(t time.Time) string {
	if t.IsZero() {
		return "Never"
	}
	return t.Format("2006-01-02 15:04:05")
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// Refresh refreshes the UI
func (ui *UI) Refresh() {
	ui.refreshTasks()
	if ui.content != nil {
		ui.content.Refresh()
	}
}
