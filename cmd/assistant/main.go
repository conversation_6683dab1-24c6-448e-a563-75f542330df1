// main.go - Koopa's Personal Development Assistant
// Cyberpunk-themed development environment with modular architecture
package main

import (
	"log"

	"assistant-go/internal/app"
	"assistant-go/internal/config"
	"assistant-go/internal/core/postgres"
	"assistant-go/internal/core/k8s"
	"assistant-go/internal/core/tasks"
	"assistant-go/internal/core/mcp"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Create the application
	application := app.New(cfg)

	// Register core modules following the planned order: Database → K8s → MCP

	// Register PostgreSQL module
	postgresManager := postgres.NewManager()
	if err := application.RegisterModule(postgresManager); err != nil {
		log.Printf("Warning: Failed to register postgres module: %v", err)
	}

	// Register Kubernetes module
	k8sManager := k8s.NewManager()
	if err := application.RegisterModule(k8sManager); err != nil {
		log.Printf("Warning: Failed to register k8s module: %v", err)
	}

	// Register Tasks module
	tasksManager := tasks.NewManager()
	if err := application.RegisterModule(tasksManager); err != nil {
		log.Printf("Warning: Failed to register tasks module: %v", err)
	}

	// Register MCP module (foundation)
	mcpManager := mcp.NewManager()
	if err := application.RegisterModule(mcpManager); err != nil {
		log.Printf("Warning: Failed to register mcp module: %v", err)
	}

	// Run the application
	if err = application.Run(); err != nil {
		log.Fatal("Application error:", err)
	}
}
