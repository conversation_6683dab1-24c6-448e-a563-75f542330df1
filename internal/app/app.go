// Package app provides the main application orchestrator for Assistant-Go
// Following Go 1.24+ best practices with proper context handling and lifecycle management
package app

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"

	"assistant-go/internal/config"
	"assistant-go/internal/core/ai"
	"assistant-go/internal/core/cloudflare"
	"assistant-go/internal/core/docker"
	"assistant-go/internal/core/k8s"
	"assistant-go/internal/core/mcp"
	"assistant-go/internal/core/postgres"
	"assistant-go/internal/core/search"
	"assistant-go/internal/core/tasks"
	"assistant-go/internal/gui"
	"assistant-go/pkg/cybertheme"
)

// App represents the main application
type App struct {
	// Configuration
	config *config.Config

	// GUI application
	fyneApp fyne.App
	gui     *gui.App

	// Application context and cancellation
	ctx    context.Context
	cancel context.CancelFunc

	// Lifecycle management
	wg     sync.WaitGroup
	logger *slog.Logger

	// Modules - will be initialized based on configuration
	modules map[string]Module

	// Application state
	running bool
	mu      sync.RWMutex
}

// Module interface defines the contract for all application modules
// Following Go philosophy: interfaces are discovered, not designed
type Module interface {
	// Initialize sets up the module with the given configuration
	Initialize(ctx context.Context, cfg *config.Config) error

	// Start begins the module's operation
	Start(ctx context.Context) error

	// Stop gracefully shuts down the module
	Stop(ctx context.Context) error

	// Name returns the module's name for identification
	Name() string

	// Health returns the current health status of the module
	Health() ModuleHealth
}

// ModuleHealth represents the health status of a module
type ModuleHealth struct {
	Status  string    `json:"status"`  // "healthy", "degraded", "unhealthy"
	Message string    `json:"message"`
	LastCheck time.Time `json:"last_check"`
}

// New creates a new application instance
func New(cfg *config.Config) *App {
	// Create context with cancellation for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())

	// Initialize structured logger
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	// Create Fyne application
	fyneApp := app.NewWithID("com.koopa.assistant-go")

	// Apply cyberpunk theme
	cyberTheme := cybertheme.NewCustomCyberTheme(
		cfg.Theme.PrimaryColor,
		cfg.Theme.SecondaryColor,
		cfg.Theme.Background,
	)
	cyberTheme.SetFontSize(cfg.Theme.FontSize)
	cyberTheme.ApplyToApp(fyneApp)

	return &App{
		config:  cfg,
		fyneApp: fyneApp,
		ctx:     ctx,
		cancel:  cancel,
		logger:  logger,
		modules: make(map[string]Module),
		running: false,
	}
}

// Run starts the application and blocks until shutdown
func (a *App) Run() error {
	a.mu.Lock()
	if a.running {
		a.mu.Unlock()
		return fmt.Errorf("application is already running")
	}
	a.running = true
	a.mu.Unlock()

	a.logger.Info("Starting Assistant-Go",
		"version", a.config.App.Version,
		"environment", a.config.App.Environment,
	)

	// Initialize the application
	if err := a.initialize(); err != nil {
		return fmt.Errorf("failed to initialize application: %w", err)
	}

	// Start all modules
	if err := a.startModules(); err != nil {
		return fmt.Errorf("failed to start modules: %w", err)
	}

	// Initialize and start GUI
	if err := a.initializeGUI(); err != nil {
		return fmt.Errorf("failed to initialize GUI: %w", err)
	}

	// Setup graceful shutdown
	a.setupGracefulShutdown()

	// Start the GUI (this blocks until the window is closed)
	a.logger.Info("GUI starting...")
	a.gui.ShowAndRun()

	// GUI has been closed, initiate shutdown
	a.logger.Info("GUI closed, initiating shutdown...")
	return a.shutdown()
}

// initialize sets up the application components
func (a *App) initialize() error {
	a.logger.Info("Initializing application components...")

	// Initialize modules based on configuration
	if err := a.initializeModules(); err != nil {
		return fmt.Errorf("failed to initialize modules: %w", err)
	}

	a.logger.Info("Application components initialized successfully")
	return nil
}

// initializeModules initializes all application modules
func (a *App) initializeModules() error {
	// Import all module packages
	postgresModule := postgres.NewManager()
	k8sModule := k8s.NewManager()
	tasksModule := tasks.NewManager()
	mcpModule := mcp.NewManager()
	dockerModule := docker.NewManager()
	cloudflareModule := cloudflare.NewManager()
	searchModule := search.NewManager()
	aiModule := ai.NewManager()

	// Register modules
	modules := []Module{
		postgresModule,
		k8sModule,
		tasksModule,
		mcpModule,
		dockerModule,
		cloudflareModule,
		searchModule,
		aiModule,
	}

	for _, module := range modules {
		if err := a.RegisterModule(module); err != nil {
			return fmt.Errorf("failed to register module %s: %w", module.Name(), err)
		}
	}

	a.logger.Info("All modules registered successfully")
	return nil
}

// initializeGUI creates and configures the GUI
func (a *App) initializeGUI() error {
	a.logger.Info("Initializing GUI...")

	var err error
	// Convert modules to interface{} map for GUI
	moduleInterfaces := make(map[string]interface{})
	for name, module := range a.modules {
		moduleInterfaces[name] = module
	}

	a.gui, err = gui.New(a.fyneApp, a.config, moduleInterfaces)
	if err != nil {
		return fmt.Errorf("failed to create GUI: %w", err)
	}

	a.logger.Info("GUI initialized successfully")
	return nil
}

// startModules starts all configured modules
func (a *App) startModules() error {
	a.logger.Info("Starting modules...")

	for name, module := range a.modules {
		a.logger.Info("Starting module", "name", name)

		if err := module.Start(a.ctx); err != nil {
			a.logger.Error("Failed to start module", "name", name, "error", err)
			return fmt.Errorf("failed to start module %s: %w", name, err)
		}

		a.logger.Info("Module started successfully", "name", name)
	}

	a.logger.Info("All modules started successfully")
	return nil
}

// setupGracefulShutdown sets up signal handling for graceful shutdown
func (a *App) setupGracefulShutdown() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		a.logger.Info("Received shutdown signal", "signal", sig.String())

		// Cancel the application context to signal shutdown
		a.cancel()

		// If GUI is running, quit it
		if a.gui != nil {
			a.fyneApp.Quit()
		}
	}()
}

// shutdown gracefully shuts down the application
func (a *App) shutdown() error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if !a.running {
		return nil
	}

	a.logger.Info("Shutting down application...")

	// Cancel context to signal all goroutines to stop
	a.cancel()

	// Stop all modules
	for name, module := range a.modules {
		a.logger.Info("Stopping module", "name", name)

		// Create a timeout context for module shutdown
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)

		if err := module.Stop(shutdownCtx); err != nil {
			a.logger.Error("Failed to stop module", "name", name, "error", err)
		} else {
			a.logger.Info("Module stopped successfully", "name", name)
		}

		cancel()
	}

	// Wait for all goroutines to finish
	done := make(chan struct{})
	go func() {
		a.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		a.logger.Info("All goroutines finished")
	case <-time.After(15 * time.Second):
		a.logger.Warn("Timeout waiting for goroutines to finish")
	}

	a.running = false
	a.logger.Info("Application shutdown complete")
	return nil
}

// RegisterModule registers a module with the application
func (a *App) RegisterModule(module Module) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	name := module.Name()
	if _, exists := a.modules[name]; exists {
		return fmt.Errorf("module %s is already registered", name)
	}

	// Initialize the module
	if err := module.Initialize(a.ctx, a.config); err != nil {
		return fmt.Errorf("failed to initialize module %s: %w", name, err)
	}

	a.modules[name] = module
	a.logger.Info("Module registered", "name", name)
	return nil
}

// GetModule returns a registered module by name
func (a *App) GetModule(name string) (Module, bool) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	module, exists := a.modules[name]
	return module, exists
}

// GetModules returns all registered modules
func (a *App) GetModules() map[string]Module {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// Return a copy to prevent external modification
	modules := make(map[string]Module)
	for name, module := range a.modules {
		modules[name] = module
	}
	return modules
}

// Health returns the overall health status of the application
func (a *App) Health() map[string]ModuleHealth {
	a.mu.RLock()
	defer a.mu.RUnlock()

	health := make(map[string]ModuleHealth)
	for name, module := range a.modules {
		health[name] = module.Health()
	}
	return health
}

// IsRunning returns whether the application is currently running
func (a *App) IsRunning() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.running
}

// Context returns the application context
func (a *App) Context() context.Context {
	return a.ctx
}

// Config returns the application configuration
func (a *App) Config() *config.Config {
	return a.config
}

// Logger returns the application logger
func (a *App) Logger() *slog.Logger {
	return a.logger
}
