// Package k8s provides Kubernetes cluster management functionality
// cluster.go - Handles cluster connections and management operations
package k8s

import (
	"context"
	"fmt"
	"log/slog"
	"path/filepath"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"

	"assistant-go/internal/config"
)

// ClusterManager handles Kubernetes cluster connections and operations
type ClusterManager struct {
	logger  *slog.Logger
	clients map[string]*ClusterClient
	mu      sync.RWMutex
	
	// Current active context
	activeContext string
}

// ClusterClient represents a connection to a Kubernetes cluster
type ClusterClient struct {
	Name       string
	Config     config.K8sContext
	Client     kubernetes.Interface
	RestConfig *rest.Config
	Status     ClusterStatus
	mu         sync.RWMutex
}

// ClusterStatus represents the status of a cluster connection
type ClusterStatus struct {
	Connected   bool
	Error       error
	LastCheck   time.Time
	Version     string
	NodeCount   int
	Namespaces  []string
}

// NewClusterManager creates a new cluster manager
func NewClusterManager() *ClusterManager {
	return &ClusterManager{
		logger:  slog.Default().With("component", "k8s-cluster"),
		clients: make(map[string]*ClusterClient),
	}
}

// AddCluster adds a new Kubernetes cluster connection
func (cm *ClusterManager) AddCluster(cfg config.K8sContext) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.clients[cfg.Name]; exists {
		return fmt.Errorf("cluster %s already exists", cfg.Name)
	}

	client := &ClusterClient{
		Name:   cfg.Name,
		Config: cfg,
		Status: ClusterStatus{
			Connected: false,
			LastCheck: time.Now(),
		},
	}

	// Attempt to connect
	if err := client.Connect(); err != nil {
		cm.logger.Error("Failed to connect to cluster", "name", cfg.Name, "error", err)
		// Store client even if it failed - user can retry
		client.Status.Error = err
	}

	cm.clients[cfg.Name] = client
	cm.logger.Info("Kubernetes cluster added", "name", cfg.Name, "connected", client.Status.Connected)

	return nil
}

// RemoveCluster removes a Kubernetes cluster connection
func (cm *ClusterManager) RemoveCluster(name string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.clients[name]; !exists {
		return fmt.Errorf("cluster %s does not exist", name)
	}

	delete(cm.clients, name)

	// Update active context if it was removed
	if cm.activeContext == name {
		cm.activeContext = ""
		// Set to first available context
		for contextName := range cm.clients {
			cm.activeContext = contextName
			break
		}
	}

	cm.logger.Info("Kubernetes cluster removed", "name", name)
	return nil
}

// GetCluster returns a Kubernetes cluster by name
func (cm *ClusterManager) GetCluster(name string) (*ClusterClient, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	client, exists := cm.clients[name]
	if !exists {
		return nil, fmt.Errorf("cluster %s does not exist", name)
	}

	return client, nil
}

// ListClusters returns all Kubernetes clusters
func (cm *ClusterManager) ListClusters() map[string]*ClusterClient {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	// Return a copy to prevent external modification
	clusters := make(map[string]*ClusterClient)
	for name, client := range cm.clients {
		clusters[name] = client
	}

	return clusters
}

// SetActiveCluster sets the active Kubernetes cluster
func (cm *ClusterManager) SetActiveCluster(name string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.clients[name]; !exists {
		return fmt.Errorf("cluster %s does not exist", name)
	}

	cm.activeContext = name
	cm.logger.Info("Active cluster changed", "cluster", name)
	return nil
}

// GetActiveCluster returns the active Kubernetes cluster
func (cm *ClusterManager) GetActiveCluster() (*ClusterClient, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	if cm.activeContext == "" {
		return nil, fmt.Errorf("no active cluster set")
	}

	client, exists := cm.clients[cm.activeContext]
	if !exists {
		return nil, fmt.Errorf("active cluster %s does not exist", cm.activeContext)
	}

	return client, nil
}

// Connect establishes a connection to the Kubernetes cluster
func (c *ClusterClient) Connect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Build config path
	configPath := c.Config.ConfigPath
	if configPath == "~/.kube/config" {
		if home := homedir.HomeDir(); home != "" {
			configPath = filepath.Join(home, ".kube", "config")
		}
	}

	// Load kubeconfig
	config, err := clientcmd.BuildConfigFromFlags("", configPath)
	if err != nil {
		// Try in-cluster config as fallback
		config, err = rest.InClusterConfig()
		if err != nil {
			c.Status.Error = err
			c.Status.Connected = false
			return fmt.Errorf("failed to load kubeconfig: %w", err)
		}
	}

	// Create client
	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		c.Status.Error = err
		c.Status.Connected = false
		return fmt.Errorf("failed to create kubernetes client: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err = client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{Limit: 1})
	if err != nil {
		c.Status.Error = err
		c.Status.Connected = false
		return fmt.Errorf("failed to connect to cluster: %w", err)
	}

	c.Client = client
	c.RestConfig = config
	c.Status.Connected = true
	c.Status.Error = nil
	c.Status.LastCheck = time.Now()

	// Get cluster info
	c.updateClusterInfo()

	return nil
}

// CheckHealth checks the health of the cluster connection
func (c *ClusterClient) CheckHealth() {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		c.mu.Lock()
		c.Status.Connected = false
		c.Status.Error = fmt.Errorf("client not initialized")
		c.Status.LastCheck = time.Now()
		c.mu.Unlock()
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{Limit: 1})

	c.mu.Lock()
	c.Status.Connected = (err == nil)
	c.Status.Error = err
	c.Status.LastCheck = time.Now()
	if err == nil {
		c.updateClusterInfo()
	}
	c.mu.Unlock()
}

// updateClusterInfo updates cluster information
func (c *ClusterClient) updateClusterInfo() {
	if c.Client == nil {
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get server version
	version, err := c.Client.Discovery().ServerVersion()
	if err == nil {
		c.Status.Version = version.String()
	}

	// Get node count
	nodes, err := c.Client.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err == nil {
		c.Status.NodeCount = len(nodes.Items)
	}

	// Get namespaces
	namespaces, err := c.Client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err == nil {
		nsNames := make([]string, len(namespaces.Items))
		for i, ns := range namespaces.Items {
			nsNames[i] = ns.Name
		}
		c.Status.Namespaces = nsNames
	}
}

// GetStatus returns the current cluster status
func (c *ClusterClient) GetStatus() ClusterStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.Status
}

// GetNamespaces returns all namespaces in the cluster
func (c *ClusterClient) GetNamespaces() ([]string, error) {
	c.mu.RLock()
	client := c.Client
	c.mu.RUnlock()

	if client == nil {
		return nil, fmt.Errorf("client not connected")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	namespaces, err := client.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list namespaces: %w", err)
	}

	names := make([]string, len(namespaces.Items))
	for i, ns := range namespaces.Items {
		names[i] = ns.Name
	}

	return names, nil
}
