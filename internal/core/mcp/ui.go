// Package mcp provides the UI components for MCP management
// Implements cyberpunk-styled MCP interface with real-time visualization
package mcp

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// UI provides the user interface for MCP management
type UI struct {
	manager *Manager
	config  *config.Config
	theme   *cybertheme.CyberTheme

	// Main components
	content         *container.Scroll
	connectionList  *widget.List
	toolList        *widget.List
	contextList     *widget.List
	messageList     *widget.List
	statusLabel     *widget.Label

	// Current state
	selectedConnection string
	selectedContext    string
	selectedTool       string
}

// NewUI creates a new MCP UI
func NewUI(manager *Manager, cfg *config.Config, theme *cybertheme.CyberTheme) *UI {
	ui := &UI{
		manager: manager,
		config:  cfg,
		theme:   theme,
	}

	ui.createComponents()
	return ui
}

// createComponents creates all UI components
func (ui *UI) createComponents() {
	// Create connection list
	ui.createConnectionList()

	// Create tool list
	ui.createToolList()

	// Create context list
	ui.createContextList()

	// Create message list
	ui.createMessageList()

	// Create status label
	ui.statusLabel = widget.NewLabel(">>> MCP MODULE (FOUNDATION) <<<")

	// Create main layout
	ui.createMainLayout()
}

// createConnectionList creates the MCP connection list
func (ui *UI) createConnectionList() {
	connections := ui.manager.ListConnections()
	connectionNames := make([]string, 0, len(connections))

	for name := range connections {
		connectionNames = append(connectionNames, name)
	}

	ui.connectionList = widget.NewList(
		func() int {
			return len(connectionNames)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add connection status icon
				widget.NewLabel("Connection Name"),
				widget.NewLabel("Status"),
				widget.NewLabel("Tools"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(connectionNames) {
				name := connectionNames[id]
				connections := ui.manager.ListConnections()
				conn, exists := connections[name]

				cont := obj.(*fyne.Container)
				nameLabel := cont.Objects[1].(*widget.Label)
				statusLabel := cont.Objects[2].(*widget.Label)
				toolsLabel := cont.Objects[3].(*widget.Label)

				nameLabel.SetText(name)

				if exists && conn != nil {
					if conn.Status.Connected {
						statusLabel.SetText("🟢 Connected")
					} else {
						statusLabel.SetText("🔴 Disconnected")
					}
					toolsLabel.SetText(fmt.Sprintf("%d tools", len(conn.Tools)))
				} else {
					statusLabel.SetText("🟡 Pending")
					toolsLabel.SetText("N/A")
				}
			}
		},
	)

	ui.connectionList.OnSelected = func(id widget.ListItemID) {
		if id < len(connectionNames) {
			ui.selectedConnection = connectionNames[id]
			ui.updateStatus(fmt.Sprintf("Selected connection: %s", ui.selectedConnection))
		}
	}
}

// createToolList creates the MCP tool list
func (ui *UI) createToolList() {
	tools := ui.manager.ListTools()
	toolNames := make([]string, 0, len(tools))

	for name := range tools {
		toolNames = append(toolNames, name)
	}

	ui.toolList = widget.NewList(
		func() int {
			return len(toolNames)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add tool icon
				widget.NewLabel("Tool Name"),
				widget.NewLabel("Description"),
				widget.NewLabel("Usage"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(toolNames) {
				name := toolNames[id]
				tools := ui.manager.ListTools()
				tool, exists := tools[name]

				cont := obj.(*fyne.Container)
				nameLabel := cont.Objects[1].(*widget.Label)
				descLabel := cont.Objects[2].(*widget.Label)
				usageLabel := cont.Objects[3].(*widget.Label)

				nameLabel.SetText(name)

				if exists && tool != nil {
					description := tool.Description
					if len(description) > 30 {
						description = description[:30] + "..."
					}
					descLabel.SetText(description)
					usageLabel.SetText(fmt.Sprintf("%d times", tool.UseCount))
				} else {
					descLabel.SetText("No description")
					usageLabel.SetText("0 times")
				}
			}
		},
	)

	ui.toolList.OnSelected = func(id widget.ListItemID) {
		if id < len(toolNames) {
			ui.selectedTool = toolNames[id]
			ui.updateStatus(fmt.Sprintf("Selected tool: %s", ui.selectedTool))
		}
	}
}

// createContextList creates the MCP context list
func (ui *UI) createContextList() {
	contexts := ui.manager.ListContexts()
	contextIDs := make([]string, 0, len(contexts))

	for id := range contexts {
		contextIDs = append(contextIDs, id)
	}

	ui.contextList = widget.NewList(
		func() int {
			return len(contextIDs)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(nil), // TODO: Add context icon
				widget.NewLabel("Context Name"),
				widget.NewLabel("Messages"),
				widget.NewLabel("Updated"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id < len(contextIDs) {
				contextID := contextIDs[id]
				contexts := ui.manager.ListContexts()
				ctx, exists := contexts[contextID]

				container := obj.(*container.Container)
				nameLabel := container.Objects[1].(*widget.Label)
				messagesLabel := container.Objects[2].(*widget.Label)
				updatedLabel := container.Objects[3].(*widget.Label)

				if exists && ctx != nil {
					nameLabel.SetText(ctx.Name)
					messagesLabel.SetText(fmt.Sprintf("%d msgs", len(ctx.Messages)))
					updatedLabel.SetText(ctx.UpdatedAt.Format("15:04"))
				} else {
					nameLabel.SetText("Unknown")
					messagesLabel.SetText("0 msgs")
					updatedLabel.SetText("N/A")
				}
			}
		},
	)

	ui.contextList.OnSelected = func(id widget.ListItemID) {
		if id < len(contextIDs) {
			ui.selectedContext = contextIDs[id]
			ui.refreshMessages()
			ui.updateStatus(fmt.Sprintf("Selected context: %s", ui.selectedContext))
		}
	}
}

// createMessageList creates the message list for the selected context
func (ui *UI) createMessageList() {
	ui.messageList = widget.NewList(
		func() int {
			if ui.selectedContext == "" {
				return 0
			}
			ctx, err := ui.manager.GetContext(ui.selectedContext)
			if err != nil {
				return 0
			}
			return len(ctx.Messages)
		},
		func() fyne.CanvasObject {
			return container.NewVBox(
				widget.NewLabel("Message Type"),
				widget.NewLabel("Content"),
				widget.NewLabel("Timestamp"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if ui.selectedContext == "" {
				return
			}

			ctx, err := ui.manager.GetContext(ui.selectedContext)
			if err != nil || id >= len(ctx.Messages) {
				return
			}

			message := ctx.Messages[id]
			container := obj.(*container.Container)
			typeLabel := container.Objects[0].(*widget.Label)
			contentLabel := container.Objects[1].(*widget.Label)
			timeLabel := container.Objects[2].(*widget.Label)

			typeLabel.SetText(message.Type)

			content := message.Content
			if len(content) > 50 {
				content = content[:50] + "..."
			}
			contentLabel.SetText(content)

			timeLabel.SetText(message.Timestamp.Format("15:04:05"))
		},
	)
}

// createMainLayout creates the main UI layout
func (ui *UI) createMainLayout() {
	// Create connections section
	connectionsSection := container.NewVBox(
		widget.NewLabel("🔗 MCP Connections"),
		widget.NewSeparator(),
		ui.connectionList,
		container.NewHBox(
			widget.NewButton("Add Connection", ui.showAddConnectionDialog),
			widget.NewButton("Test Connection", ui.testSelectedConnection),
			widget.NewButton("Refresh", ui.refreshConnections),
		),
	)

	// Create tools section
	toolsSection := container.NewVBox(
		widget.NewLabel("🛠️ Available Tools"),
		widget.NewSeparator(),
		ui.toolList,
		container.NewHBox(
			widget.NewButton("Call Tool", ui.callSelectedTool),
			widget.NewButton("Show Schema", ui.showToolSchema),
			widget.NewButton("Refresh", ui.refreshTools),
		),
	)

	// Create contexts section
	contextsSection := container.NewVBox(
		widget.NewLabel("💬 MCP Contexts"),
		widget.NewSeparator(),
		ui.contextList,
		container.NewHBox(
			widget.NewButton("New Context", ui.createNewContext),
			widget.NewButton("Delete Context", ui.deleteSelectedContext),
		),
	)

	// Create messages section
	messagesSection := container.NewVBox(
		widget.NewLabel("📝 Context Messages"),
		widget.NewSeparator(),
		container.NewScroll(ui.messageList),
		container.NewHBox(
			widget.NewButton("Add Message", ui.addMessage),
			widget.NewButton("Clear Messages", ui.clearMessages),
		),
	)

	// Create main container with scrolling support
	mainContainer := container.NewVBox(
		widget.NewRichTextFromMarkdown(`
# 🤖 MCP Monitor

**MODEL CONTEXT PROTOCOL VISUALIZATION**

> **STATUS:** Foundation Implementation Complete
> **WAITING FOR:** Official Go MCP Release

This module provides the foundation for Model Context Protocol integration.
When the official Go MCP implementation is released, this interface will
provide real-time visualization of AI interactions, context flow debugging,
and response analysis.

## Planned Features:
- **Real-time MCP Visualization** - See AI tool calls as they happen
- **Context Flow Debugging** - Understand how context flows between tools
- **Response Analysis** - Monitor model responses and context usage
- **Tool Call Inspection** - Debug and analyze tool interactions

---
`),
		connectionsSection,
		widget.NewSeparator(),
		toolsSection,
		widget.NewSeparator(),
		contextsSection,
		widget.NewSeparator(),
		messagesSection,
		widget.NewSeparator(),
		ui.statusLabel,
	)

	// Wrap in scroll container to handle overflow
	ui.content = container.NewScroll(mainContainer)
	ui.content.SetMinSize(fyne.NewSize(800, 600))
}

// showAddConnectionDialog shows the add connection dialog (placeholder)
func (ui *UI) showAddConnectionDialog() {
	ui.updateStatus("Add Connection feature pending MCP implementation")
	// TODO: Implement when MCP is available
}

// testSelectedConnection tests the selected MCP connection (placeholder)
func (ui *UI) testSelectedConnection() {
	if ui.selectedConnection == "" {
		ui.updateStatus("Error: No connection selected")
		return
	}

	ui.updateStatus(fmt.Sprintf("Testing connection '%s' (placeholder)", ui.selectedConnection))
	// TODO: Implement when MCP is available
}

// callSelectedTool calls the selected MCP tool (placeholder)
func (ui *UI) callSelectedTool() {
	if ui.selectedTool == "" {
		ui.updateStatus("Error: No tool selected")
		return
	}

	ui.updateStatus(fmt.Sprintf("Calling tool '%s' (placeholder)", ui.selectedTool))
	// TODO: Implement when MCP is available
}

// showToolSchema shows the schema for the selected tool (placeholder)
func (ui *UI) showToolSchema() {
	if ui.selectedTool == "" {
		ui.updateStatus("Error: No tool selected")
		return
	}

	ui.updateStatus(fmt.Sprintf("Showing schema for tool '%s' (placeholder)", ui.selectedTool))
	// TODO: Implement when MCP is available
}

// createNewContext creates a new MCP context
func (ui *UI) createNewContext() {
	contextName := fmt.Sprintf("Context_%d", time.Now().Unix())

	_, err := ui.manager.CreateContext(contextName)
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error creating context: %s", err.Error()))
		return
	}

	ui.refreshContexts()
	ui.updateStatus(fmt.Sprintf("Created new context: %s", contextName))
}

// deleteSelectedContext deletes the selected context (placeholder)
func (ui *UI) deleteSelectedContext() {
	if ui.selectedContext == "" {
		ui.updateStatus("Error: No context selected")
		return
	}

	ui.updateStatus(fmt.Sprintf("Delete context '%s' (placeholder)", ui.selectedContext))
	// TODO: Implement context deletion
}

// addMessage adds a message to the selected context (placeholder)
func (ui *UI) addMessage() {
	if ui.selectedContext == "" {
		ui.updateStatus("Error: No context selected")
		return
	}

	// Add a placeholder message
	err := ui.manager.AddMessage(ui.selectedContext, "user", "Test message")
	if err != nil {
		ui.updateStatus(fmt.Sprintf("Error adding message: %s", err.Error()))
		return
	}

	ui.refreshMessages()
	ui.updateStatus("Message added to context")
}

// clearMessages clears messages from the selected context (placeholder)
func (ui *UI) clearMessages() {
	if ui.selectedContext == "" {
		ui.updateStatus("Error: No context selected")
		return
	}

	ui.updateStatus(fmt.Sprintf("Clear messages from context '%s' (placeholder)", ui.selectedContext))
	// TODO: Implement message clearing
}

// refreshConnections refreshes the connection list
func (ui *UI) refreshConnections() {
	ui.connectionList.Refresh()
	ui.updateStatus("Connection list refreshed")
}

// refreshTools refreshes the tool list
func (ui *UI) refreshTools() {
	ui.toolList.Refresh()
	ui.updateStatus("Tool list refreshed")
}

// refreshContexts refreshes the context list
func (ui *UI) refreshContexts() {
	ui.contextList.Refresh()
	ui.updateStatus("Context list refreshed")
}

// refreshMessages refreshes the message list
func (ui *UI) refreshMessages() {
	ui.messageList.Refresh()
}

// updateStatus updates the status label
func (ui *UI) updateStatus(message string) {
	ui.statusLabel.SetText(fmt.Sprintf(">>> %s <<<", message))
}

// Content returns the UI content
func (ui *UI) Content() fyne.CanvasObject {
	return ui.content
}

// Refresh refreshes the UI
func (ui *UI) Refresh() {
	ui.refreshConnections()
	ui.refreshTools()
	ui.refreshContexts()
	ui.refreshMessages()
	if ui.content != nil {
		ui.content.Refresh()
	}
}
