// Package ai provides AI integration using langchain-go
// manager.go - Main AI module manager
package ai

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/anthropic"
	"github.com/tmc/langchaingo/llms/googleai"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"

	"assistant-go/internal/app"
	"assistant-go/internal/config"
)

// Manager handles AI operations using langchain-go
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// LLM providers
	providers map[string]llms.Model
	active    string

	// Memory and conversation management
	memory memory.ConversationBuffer
	
	// Agent components
	agent    *Agent
	planner  *TaskPlanner
	executor *TaskExecutor

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	mu      sync.RWMutex

	// Conversation history
	conversations map[string]*Conversation
}

// Conversation represents an AI conversation session
type Conversation struct {
	ID        string                `json:"id"`
	Title     string                `json:"title"`
	Messages  []schema.ChatMessage  `json:"messages"`
	Created   time.Time             `json:"created"`
	Updated   time.Time             `json:"updated"`
	Context   map[string]interface{} `json:"context,omitempty"`
}

// TaskRequest represents a task request to the AI agent
type TaskRequest struct {
	ID          string                 `json:"id"`
	Description string                 `json:"description"`
	Context     map[string]interface{} `json:"context,omitempty"`
	Priority    int                    `json:"priority"`
	Deadline    *time.Time             `json:"deadline,omitempty"`
	Created     time.Time              `json:"created"`
}

// TaskResponse represents the AI agent's response to a task
type TaskResponse struct {
	ID       string                 `json:"id"`
	TaskID   string                 `json:"task_id"`
	Status   string                 `json:"status"`
	Result   string                 `json:"result"`
	Actions  []string               `json:"actions,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	Created  time.Time              `json:"created"`
}

// NewManager creates a new AI manager
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	return &Manager{
		providers:     make(map[string]llms.Model),
		conversations: make(map[string]*Conversation),
		ctx:           ctx,
		cancel:        cancel,
		logger:        slog.Default().With("component", "ai"),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.config = cfg
	m.logger.Info("Initializing AI module")

	// Initialize LLM providers
	if err := m.initializeProviders(); err != nil {
		return fmt.Errorf("failed to initialize AI providers: %w", err)
	}

	// Initialize memory
	m.memory = memory.NewConversationBuffer()

	// Initialize agent components
	if err := m.initializeAgent(); err != nil {
		return fmt.Errorf("failed to initialize AI agent: %w", err)
	}

	m.logger.Info("AI module initialized successfully")
	return nil
}

// Start implements the Module interface
func (m *Manager) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("AI module is already running")
	}

	m.logger.Info("Starting AI module")

	// Start agent components
	if m.agent != nil {
		if err := m.agent.Start(); err != nil {
			return fmt.Errorf("failed to start AI agent: %w", err)
		}
	}

	m.running = true
	m.logger.Info("AI module started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping AI module")

	// Stop agent components
	if m.agent != nil {
		m.agent.Stop()
	}

	m.cancel()
	m.running = false

	m.logger.Info("AI module stopped")
	return nil
}

// Health implements the Module interface
func (m *Manager) Health() app.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := app.ModuleHealth{
		Status:    "healthy",
		Message:   "AI services available",
		LastCheck: time.Now(),
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
		return health
	}

	// Check provider availability
	availableProviders := 0
	for name, provider := range m.providers {
		if provider != nil {
			availableProviders++
		} else {
			m.logger.Warn("AI provider not available", "provider", name)
		}
	}

	if availableProviders == 0 {
		health.Status = "unhealthy"
		health.Message = "No AI providers available"
		return health
	}

	conversationCount := len(m.conversations)
	health.Message = fmt.Sprintf("%d providers, %d conversations", availableProviders, conversationCount)

	return health
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "AI"
}

// initializeProviders initializes LLM providers based on configuration
func (m *Manager) initializeProviders() error {
	for name, providerConfig := range m.config.AI.Providers {
		if !providerConfig.Enabled || providerConfig.APIKey == "" {
			m.logger.Info("Skipping disabled AI provider", "provider", name)
			continue
		}

		var provider llms.Model
		var err error

		switch name {
		case "claude":
			provider, err = anthropic.New(
				anthropic.WithToken(providerConfig.APIKey),
				anthropic.WithModel(providerConfig.Model),
			)
		case "gemini":
			provider, err = googleai.New(
				googleai.WithAPIKey(providerConfig.APIKey),
				googleai.WithDefaultModel(providerConfig.Model),
			)
		default:
			m.logger.Warn("Unknown AI provider", "provider", name)
			continue
		}

		if err != nil {
			m.logger.Error("Failed to initialize AI provider", "provider", name, "error", err)
			continue
		}

		m.providers[name] = provider
		m.logger.Info("AI provider initialized", "provider", name, "model", providerConfig.Model)

		// Set as active if it's the default
		if name == m.config.AI.Default {
			m.active = name
		}
	}

	// Set first available provider as active if default is not available
	if m.active == "" && len(m.providers) > 0 {
		for name := range m.providers {
			m.active = name
			break
		}
	}

	if m.active != "" {
		m.logger.Info("Active AI provider set", "provider", m.active)
	}

	return nil
}

// initializeAgent initializes the AI agent components
func (m *Manager) initializeAgent() error {
	if len(m.providers) == 0 {
		m.logger.Info("No AI providers available, skipping agent initialization")
		return nil
	}

	// Initialize agent
	agent, err := NewAgent(m.providers[m.active], m.memory)
	if err != nil {
		return fmt.Errorf("failed to create AI agent: %w", err)
	}
	m.agent = agent

	// Initialize task planner
	planner, err := NewTaskPlanner(m.providers[m.active])
	if err != nil {
		return fmt.Errorf("failed to create task planner: %w", err)
	}
	m.planner = planner

	// Initialize task executor
	executor, err := NewTaskExecutor(m.providers[m.active])
	if err != nil {
		return fmt.Errorf("failed to create task executor: %w", err)
	}
	m.executor = executor

	return nil
}

// Chat sends a message to the AI and returns the response
func (m *Manager) Chat(message string, conversationID string) (string, error) {
	if m.agent == nil {
		return "", fmt.Errorf("AI agent not available")
	}

	// Get or create conversation
	conversation := m.getOrCreateConversation(conversationID)

	// Add user message to conversation
	userMessage := schema.HumanChatMessage{Content: message}
	conversation.Messages = append(conversation.Messages, userMessage)

	// Get AI response
	response, err := m.agent.Chat(message, conversation.Context)
	if err != nil {
		return "", fmt.Errorf("AI chat failed: %w", err)
	}

	// Add AI response to conversation
	aiMessage := schema.AIChatMessage{Content: response}
	conversation.Messages = append(conversation.Messages, aiMessage)
	conversation.Updated = time.Now()

	return response, nil
}

// ExecuteTask executes a task using the AI agent
func (m *Manager) ExecuteTask(request TaskRequest) (*TaskResponse, error) {
	if m.agent == nil {
		return nil, fmt.Errorf("AI agent not available")
	}

	return m.agent.ExecuteTask(request)
}

// PlanTask creates a plan for executing a task
func (m *Manager) PlanTask(description string, context map[string]interface{}) (*TaskPlan, error) {
	if m.planner == nil {
		return nil, fmt.Errorf("task planner not available")
	}

	return m.planner.CreatePlan(description, context)
}

// GetConversation retrieves a conversation by ID
func (m *Manager) GetConversation(id string) (*Conversation, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	conversation, exists := m.conversations[id]
	if !exists {
		return nil, fmt.Errorf("conversation not found: %s", id)
	}

	return conversation, nil
}

// ListConversations returns all conversations
func (m *Manager) ListConversations() []*Conversation {
	m.mu.RLock()
	defer m.mu.RUnlock()

	conversations := make([]*Conversation, 0, len(m.conversations))
	for _, conv := range m.conversations {
		conversations = append(conversations, conv)
	}

	return conversations
}

// DeleteConversation deletes a conversation
func (m *Manager) DeleteConversation(id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.conversations[id]; !exists {
		return fmt.Errorf("conversation not found: %s", id)
	}

	delete(m.conversations, id)
	m.logger.Info("Conversation deleted", "id", id)
	return nil
}

// getOrCreateConversation gets an existing conversation or creates a new one
func (m *Manager) getOrCreateConversation(id string) *Conversation {
	m.mu.Lock()
	defer m.mu.Unlock()

	if conversation, exists := m.conversations[id]; exists {
		return conversation
	}

	// Create new conversation
	conversation := &Conversation{
		ID:       id,
		Title:    fmt.Sprintf("Conversation %s", id[:8]),
		Messages: make([]schema.ChatMessage, 0),
		Created:  time.Now(),
		Updated:  time.Now(),
		Context:  make(map[string]interface{}),
	}

	m.conversations[id] = conversation
	return conversation
}

// SetActiveProvider sets the active AI provider
func (m *Manager) SetActiveProvider(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.providers[name]; !exists {
		return fmt.Errorf("provider not found: %s", name)
	}

	m.active = name
	m.logger.Info("Active AI provider changed", "provider", name)

	// Reinitialize agent with new provider
	if err := m.initializeAgent(); err != nil {
		return fmt.Errorf("failed to reinitialize agent: %w", err)
	}

	return nil
}

// GetActiveProvider returns the name of the active AI provider
func (m *Manager) GetActiveProvider() string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.active
}

// GetAvailableProviders returns the list of available AI providers
func (m *Manager) GetAvailableProviders() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	providers := make([]string, 0, len(m.providers))
	for name := range m.providers {
		providers = append(providers, name)
	}

	return providers
}

// TODO: Implement conversation export/import
// TODO: Implement AI model fine-tuning support
// TODO: Implement custom prompt templates
// TODO: Implement AI agent tool integration
// TODO: Implement conversation search and filtering
// TODO: Implement AI usage analytics and cost tracking
