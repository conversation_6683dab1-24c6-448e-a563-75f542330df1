// Package types provides shared types for Assistant-Go
// module.go - Module interface and health types
package types

import (
	"context"
	"time"

	"assistant-go/internal/config"
)

// Module represents a functional module in the application
type Module interface {
	// Name returns the module name
	Name() string

	// Initialize initializes the module with configuration
	Initialize(ctx context.Context, cfg *config.Config) error

	// Start starts the module
	Start() error

	// Stop stops the module
	Stop() error

	// Health returns the current health status
	Health() ModuleHealth
}

// ModuleHealth represents the health status of a module
type ModuleHealth struct {
	Status    string    `json:"status"`    // healthy, degraded, unhealthy, pending
	Message   string    `json:"message"`   // Human-readable status message
	LastCheck time.Time `json:"last_check"` // When the health was last checked
}
