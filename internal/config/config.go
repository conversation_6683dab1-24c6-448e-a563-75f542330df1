// Package config provides configuration management for Assistant-Go
// Following Go 1.24+ best practices with proper error handling and context support
package config

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gopkg.in/yaml.v3"
)

// Config represents the complete application configuration
type Config struct {
	// Application settings
	App AppConfig `yaml:"app"`

	// GUI and theme settings
	Theme ThemeConfig `yaml:"theme"`

	// Database connections
	Database DatabaseConfig `yaml:"database"`

	// Kubernetes settings
	K8s K8sConfig `yaml:"k8s"`

	// Task management settings
	Tasks TasksConfig `yaml:"tasks"`

	// AI integration settings
	AI AIConfig `yaml:"ai"`

	// Docker settings
	Docker DockerConfig `yaml:"docker"`

	// Cloudflare settings
	Cloudflare CloudflareConfig `yaml:"cloudflare"`

	// SearXNG search settings
	Search SearchConfig `yaml:"search"`

	// Logging configuration
	Logging LoggingConfig `yaml:"logging"`
}

// AppConfig contains general application settings
type AppConfig struct {
	Name        string        `yaml:"name"`
	Version     string        `yaml:"version"`
	Environment string        `yaml:"environment"`
	Debug       bool          `yaml:"debug"`
	Timeout     time.Duration `yaml:"timeout"`
}

// ThemeConfig defines the cyberpunk theme settings
type ThemeConfig struct {
	Style         string            `yaml:"style"`
	PrimaryColor  string            `yaml:"primary_color"`
	SecondaryColor string           `yaml:"secondary_color"`
	Background    string            `yaml:"background"`
	FontFamily    string            `yaml:"font_family"`
	FontSize      float32           `yaml:"font_size"`
	Animations    bool              `yaml:"animations"`
	CustomColors  map[string]string `yaml:"custom_colors,omitempty"`
}

// DatabaseConfig contains PostgreSQL connection settings
type DatabaseConfig struct {
	Connections []DatabaseConnection `yaml:"connections"`
	PoolSize    int                  `yaml:"pool_size"`
	Timeout     time.Duration        `yaml:"timeout"`
}

// DatabaseConnection represents a single database connection
type DatabaseConnection struct {
	Name     string `yaml:"name"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password,omitempty"` // Will be loaded from environment or keychain
	SSLMode  string `yaml:"ssl_mode"`
	Enabled  bool   `yaml:"enabled"`
}

// K8sConfig contains Kubernetes cluster settings
type K8sConfig struct {
	Contexts       []K8sContext  `yaml:"contexts"`
	DefaultContext string        `yaml:"default_context"`
	Timeout        time.Duration `yaml:"timeout"`
	RefreshRate    time.Duration `yaml:"refresh_rate"`
}

// K8sContext represents a Kubernetes context configuration
type K8sContext struct {
	Name       string `yaml:"name"`
	ConfigPath string `yaml:"config_path"`
	Namespace  string `yaml:"namespace"`
	Enabled    bool   `yaml:"enabled"`
}

// TasksConfig contains task management settings
type TasksConfig struct {
	MakefilePath string   `yaml:"makefile_path"`
	TaskfilePath string   `yaml:"taskfile_path"`
	AutoDetect   bool     `yaml:"auto_detect"`
	Favorites    []string `yaml:"favorites,omitempty"`
}

// AIConfig contains AI integration settings
type AIConfig struct {
	Providers map[string]AIProvider `yaml:"providers"`
	Default   string                `yaml:"default"`
	Timeout   time.Duration         `yaml:"timeout"`
}

// AIProvider represents an AI service provider configuration
type AIProvider struct {
	APIKey   string            `yaml:"api_key,omitempty"` // Will be loaded from environment
	BaseURL  string            `yaml:"base_url,omitempty"`
	Model    string            `yaml:"model"`
	Enabled  bool              `yaml:"enabled"`
	Settings map[string]string `yaml:"settings,omitempty"`
}

// DockerConfig contains Docker integration settings
type DockerConfig struct {
	Endpoint    string        `yaml:"endpoint"`
	APIVersion  string        `yaml:"api_version"`
	Timeout     time.Duration `yaml:"timeout"`
	TLSVerify   bool          `yaml:"tls_verify"`
	CertPath    string        `yaml:"cert_path,omitempty"`
	KeyPath     string        `yaml:"key_path,omitempty"`
	CAPath      string        `yaml:"ca_path,omitempty"`
	Enabled     bool          `yaml:"enabled"`
	AutoRefresh time.Duration `yaml:"auto_refresh"`
}

// CloudflareConfig contains Cloudflare API settings
type CloudflareConfig struct {
	APIToken string        `yaml:"api_token,omitempty"` // Will be loaded from environment
	APIEmail string        `yaml:"api_email,omitempty"`
	APIKey   string        `yaml:"api_key,omitempty"`   // Will be loaded from environment
	Timeout  time.Duration `yaml:"timeout"`
	Enabled  bool          `yaml:"enabled"`
	Zones    []string      `yaml:"zones,omitempty"` // Specific zones to manage
}

// SearchConfig contains SearXNG search engine settings
type SearchConfig struct {
	BaseURL     string            `yaml:"base_url"`
	Timeout     time.Duration     `yaml:"timeout"`
	MaxResults  int               `yaml:"max_results"`
	Categories  []string          `yaml:"categories"`
	Engines     []string          `yaml:"engines,omitempty"`
	Language    string            `yaml:"language"`
	SafeSearch  int               `yaml:"safe_search"`
	Enabled     bool              `yaml:"enabled"`
	CustomHeaders map[string]string `yaml:"custom_headers,omitempty"`
}

// LoggingConfig contains logging settings
type LoggingConfig struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"`
	Output     string `yaml:"output"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// DefaultConfig returns the default configuration
func DefaultConfig() *Config {
	return &Config{
		App: AppConfig{
			Name:        "Assistant-Go",
			Version:     "1.0.0-dev",
			Environment: "development",
			Debug:       true,
			Timeout:     30 * time.Second,
		},
		Theme: ThemeConfig{
			Style:          "cyber",
			PrimaryColor:   "#0096ff",  // Cyberpunk blue
			SecondaryColor: "#00ffff",  // Bright cyan
			Background:     "matrix",
			FontFamily:     "JetBrains Mono",
			FontSize:       12.0,
			Animations:     true,
			CustomColors: map[string]string{
				"terminal_green": "#00ff00",  // Keep green for text
				"neon_blue":      "#0096ff",  // Primary blue
				"warning_amber":  "#ffaa00",  // Amber warning
				"error_red":      "#ff3264",  // Pink-red error
				"accent_cyan":    "#00ffff",  // Cyan accent
			},
		},
		Database: DatabaseConfig{
			Connections: []DatabaseConnection{
				{
					Name:     "default",
					Host:     "localhost",
					Port:     5432,
					Database: "postgres",
					Username: "postgres",
					SSLMode:  "prefer",
					Enabled:  false, // Disabled by default until configured
				},
			},
			PoolSize: 10,
			Timeout:  10 * time.Second,
		},
		K8s: K8sConfig{
			Contexts: []K8sContext{
				{
					Name:       "local",
					ConfigPath: "~/.kube/config",
					Namespace:  "default",
					Enabled:    false, // Disabled by default until configured
				},
			},
			DefaultContext: "local",
			Timeout:        30 * time.Second,
			RefreshRate:    5 * time.Second,
		},
		Tasks: TasksConfig{
			MakefilePath: "./Makefile",
			TaskfilePath: "./Taskfile.yml",
			AutoDetect:   true,
			Favorites:    []string{"build", "test", "run"},
		},
		AI: AIConfig{
			Providers: map[string]AIProvider{
				"claude": {
					Model:   "claude-3-sonnet-20240229",
					Enabled: false, // Disabled by default until API key is provided
				},
				"gemini": {
					Model:   "gemini-pro",
					Enabled: false, // Disabled by default until API key is provided
				},
			},
			Default: "claude",
			Timeout: 60 * time.Second,
		},
		Docker: DockerConfig{
			Endpoint:    "unix:///var/run/docker.sock", // Default Docker socket
			APIVersion:  "1.41",                        // Docker API version
			Timeout:     30 * time.Second,
			TLSVerify:   false,
			Enabled:     false, // Disabled by default until Docker is available
			AutoRefresh: 5 * time.Second,
		},
		Cloudflare: CloudflareConfig{
			Timeout: 30 * time.Second,
			Enabled: false, // Disabled by default until API credentials are provided
		},
		Search: SearchConfig{
			BaseURL:    "https://searx.be", // Public SearXNG instance
			Timeout:    15 * time.Second,
			MaxResults: 20,
			Categories: []string{"it", "science"},
			Language:   "en",
			SafeSearch: 1, // Moderate safe search
			Enabled:    true, // Enabled by default with public instance
		},
		Logging: LoggingConfig{
			Level:      "info",
			Format:     "json",
			Output:     "stdout",
			MaxSize:    100, // MB
			MaxBackups: 3,
			MaxAge:     28, // days
		},
	}
}

// Load loads configuration from file with environment variable substitution
func Load() (*Config, error) {
	return LoadWithContext(context.Background())
}

// LoadWithContext loads configuration with context support
func LoadWithContext(ctx context.Context) (*Config, error) {
	cfg := DefaultConfig()

	// Try to load from user config directory first
	configPaths := []string{
		filepath.Join(os.Getenv("HOME"), ".assistant-go", "config.yaml"),
		filepath.Join(os.Getenv("HOME"), ".config", "assistant-go", "config.yaml"),
		"./config.yaml",
		"./configs/config.yaml",
	}

	var configLoaded bool
	for _, path := range configPaths {
		if err := loadFromFile(ctx, cfg, path); err == nil {
			configLoaded = true
			break
		}
	}

	if !configLoaded {
		// No config file found, use defaults
		fmt.Printf("No configuration file found, using defaults\n")
	}

	// Load sensitive data from environment variables
	if err := loadFromEnvironment(cfg); err != nil {
		return nil, fmt.Errorf("failed to load environment variables: %w", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return cfg, nil
}

// loadFromFile loads configuration from a YAML file
func loadFromFile(ctx context.Context, cfg *Config, path string) error {
	// Expand home directory
	if filepath.HasPrefix(path, "~/") {
		home, err := os.UserHomeDir()
		if err != nil {
			return fmt.Errorf("failed to get user home directory: %w", err)
		}
		path = filepath.Join(home, path[2:])
	}

	data, err := os.ReadFile(path)
	if err != nil {
		return fmt.Errorf("failed to read config file %s: %w", path, err)
	}

	if err := yaml.Unmarshal(data, cfg); err != nil {
		return fmt.Errorf("failed to parse config file %s: %w", path, err)
	}

	fmt.Printf("Configuration loaded from: %s\n", path)
	return nil
}

// loadFromEnvironment loads sensitive configuration from environment variables
func loadFromEnvironment(cfg *Config) error {
	// Load database passwords
	for i := range cfg.Database.Connections {
		conn := &cfg.Database.Connections[i]
		if envKey := fmt.Sprintf("DB_%s_PASSWORD", conn.Name); os.Getenv(envKey) != "" {
			conn.Password = os.Getenv(envKey)
		}
	}

	// Load AI API keys
	for name, provider := range cfg.AI.Providers {
		if envKey := fmt.Sprintf("%s_API_KEY", name); os.Getenv(envKey) != "" {
			provider.APIKey = os.Getenv(envKey)
			provider.Enabled = true // Enable if API key is provided
			cfg.AI.Providers[name] = provider
		}
	}

	// Load Cloudflare API credentials
	if token := os.Getenv("CLOUDFLARE_API_TOKEN"); token != "" {
		cfg.Cloudflare.APIToken = token
		cfg.Cloudflare.Enabled = true
	} else if email := os.Getenv("CLOUDFLARE_API_EMAIL"); email != "" {
		if key := os.Getenv("CLOUDFLARE_API_KEY"); key != "" {
			cfg.Cloudflare.APIEmail = email
			cfg.Cloudflare.APIKey = key
			cfg.Cloudflare.Enabled = true
		}
	}

	// Load Docker endpoint from environment if set
	if endpoint := os.Getenv("DOCKER_HOST"); endpoint != "" {
		cfg.Docker.Endpoint = endpoint
	}

	// Load SearXNG URL from environment if set
	if baseURL := os.Getenv("SEARXNG_URL"); baseURL != "" {
		cfg.Search.BaseURL = baseURL
	}

	return nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate theme colors
	if c.Theme.PrimaryColor == "" {
		return fmt.Errorf("theme.primary_color cannot be empty")
	}

	// Validate database connections
	for _, conn := range c.Database.Connections {
		if conn.Enabled && conn.Host == "" {
			return fmt.Errorf("database connection %s: host cannot be empty", conn.Name)
		}
	}

	// Validate K8s contexts
	for _, ctx := range c.K8s.Contexts {
		if ctx.Enabled && ctx.ConfigPath == "" {
			return fmt.Errorf("k8s context %s: config_path cannot be empty", ctx.Name)
		}
	}

	return nil
}

// Save saves the current configuration to file
func (c *Config) Save(path string) error {
	// Expand home directory
	if filepath.HasPrefix(path, "~/") {
		home, err := os.UserHomeDir()
		if err != nil {
			return fmt.Errorf("failed to get user home directory: %w", err)
		}
		path = filepath.Join(home, path[2:])
	}

	// Create directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	data, err := yaml.Marshal(c)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}
